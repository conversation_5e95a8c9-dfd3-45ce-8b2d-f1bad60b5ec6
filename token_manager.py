#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token管理模块
用于在登录程序和股票软件之间共享token，不生成文件
"""

import threading
import time

class TokenManager:
    """Token管理器 - 单例模式"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TokenManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._token = None
            self._timestamp = None
            self._lock = threading.Lock()
            self._initialized = True
    
    def set_token(self, token):
        """设置token"""
        with self._lock:
            self._token = token
            self._timestamp = time.time()
            print(f"Token已保存到内存: {token[:10] if token else 'None'}...")
    
    def get_token(self):
        """获取token"""
        with self._lock:
            if self._token:
                print(f"从内存读取Token: {self._token[:10]}...")
                return self._token
            return None
    
    def clear_token(self):
        """清除token"""
        with self._lock:
            self._token = None
            self._timestamp = None
            print("Token已从内存中清除")
    
    def is_token_valid(self):
        """检查token是否有效"""
        with self._lock:
            return self._token is not None and len(self._token) > 10

# 全局token管理器实例
_token_manager = TokenManager()

def set_token(token):
    """设置全局token"""
    _token_manager.set_token(token)

def get_token():
    """获取全局token"""
    return _token_manager.get_token()

def clear_token():
    """清除全局token"""
    _token_manager.clear_token()

def is_token_valid():
    """检查token是否有效"""
    return _token_manager.is_token_valid()
