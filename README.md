# 量化股票软件使用说明

## 软件介绍
这是一个集成了登录注册和股票分析功能的量化交易软件，将登录注册界面和股票分析软件打包成单个exe文件，方便用户使用。

## 主要功能
1. **用户登录注册** - Tushare账户登录和注册，支持验证码自动获取
2. **股票数据分析** - 实时股票数据获取和分析，支持API限流控制
3. **策略回测** - 多种技术指标策略回测，包含MACD、KDJ等经典策略
4. **多股票回测** - 支持板块股票批量回测，可按行业板块加载股票
5. **自定义策略** - 支持用户自定义交易策略编写和回测
6. **网页交易** - 集成网页交易功能（需要浏览器支持）

## 核心特性
- ✅ **单文件部署** - 所有功能打包在一个exe文件中
- ✅ **内存Token管理** - 登录成功后Token保存在内存中，不生成文件
- ✅ **API限流保护** - 自动控制每分钟API调用次数，避免超限
- ✅ **板块股票加载** - 支持按板块批量加载股票进行回测
- ✅ **友好错误处理** - 获取股票列表失败时显示"欢迎使用"而非错误信息
- ✅ **进程管理** - 登录界面和股票软件进程管理优化

## 使用方法

### 快速开始
1. 双击运行 `量化股票软件.exe`
2. 在登录界面输入手机号
3. 输入图片验证码
4. 点击登录（如果是新用户，选择注册）
5. 登录成功后自动启动股票分析软件
6. 登录界面会自动隐藏到后台

### 功能使用
- **股票分析**: 在股票分析选项卡中输入股票代码查看K线图和技术指标
- **策略回测**: 选择预设策略或自定义策略进行历史数据回测
- **多股票回测**:
  - 选择板块类型（主板、创业板、科创板等）
  - 点击"加载板块股票"批量添加股票
  - 设置回测参数进行批量回测
- **自定义策略**: 编写Python代码实现自定义交易策略

## 注意事项
- 首次使用需要注册Tushare账户
- 确保网络连接稳定，API调用需要网络支持
- 大规模回测（500+股票）可能需要较长时间
- 建议在稳定的网络环境下使用
- 软件会自动控制API调用频率，请耐心等待

## 系统要求
- Windows 10/11 操作系统
- 稳定的网络连接
- 至少2GB可用内存
- 建议安装Edge或Chrome浏览器（用于验证码显示）

## 故障排除
1. **程序无法启动**: 检查是否有杀毒软件拦截
2. **验证码无法显示**: 检查网络连接，确保能访问Tushare网站
3. **登录失败**: 确认手机号和验证码输入正确
4. **回测速度慢**: 这是正常现象，API有频率限制
5. **股票数据获取失败**: 检查网络连接和Tushare服务状态

## 技术支持
如有问题请联系技术支持。

---
*版本: 1.0 | 更新日期: 2025-01-07*
