@echo off
chcp 65001 >nul
echo ========================================
echo 量化股票软件一键打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境，请先安装Python
    pause
    exit /b 1
)
echo ✅ Python环境正常

echo.
echo 正在检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo 📦 正在安装PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller安装失败
        pause
        exit /b 1
    )
    echo ✅ PyInstaller安装成功
) else (
    echo ✅ PyInstaller已安装
)

echo.
echo 正在检查必要文件...
if not exist "登录注册.py" (
    echo ❌ 缺少文件: 登录注册.py
    pause
    exit /b 1
)
if not exist "股票看图软件_增强版.py" (
    echo ❌ 缺少文件: 股票看图软件_增强版.py
    pause
    exit /b 1
)
if not exist "token_manager.py" (
    echo ❌ 缺少文件: token_manager.py
    pause
    exit /b 1
)
echo ✅ 必要文件检查完成

echo.
echo 🔨 开始打包...
echo 这可能需要几分钟时间，请耐心等待...
echo.

python -m PyInstaller 量化股票软件.spec --clean --noconfirm

if errorlevel 1 (
    echo.
    echo ❌ 打包失败，请检查错误信息
    pause
    exit /b 1
)

echo.
echo 🎉 打包完成！
echo.
echo 📁 可执行文件位置: dist\量化股票软件.exe
echo 📖 使用说明: README.md
echo.
echo 您现在可以运行 dist\量化股票软件.exe 来启动程序
echo.

pause
