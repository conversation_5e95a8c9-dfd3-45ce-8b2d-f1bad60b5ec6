#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整打包脚本
将登录注册.py作为启动入口，打包成单个exe文件
包含所有依赖模块和功能
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_complete_spec_file():
    """创建完整的PyInstaller配置文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('token_manager.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
]

a = Analysis(
    ['登录注册.py'],  # 以登录注册.py作为启动入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理库
        'pandas',
        'numpy',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.backends.backend_tkagg',
        
        # 网络和API库
        'requests',
        'tushare',
        'lxml',
        'bs4',
        'urllib3',
        
        # 浏览器自动化库
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 图像处理库
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # Excel处理库
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        
        # 系统和工具库
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'base64',
        'io',
        'hashlib',
        'gzip',
        'pickle',
        'collections',
        'concurrent.futures',
        'psutil',
        'platform',
        
        # 自定义模块
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        'token_manager',
        '浏览器驱动管理',
        '股票看图软件_增强版',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'spyder',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='量化股票软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
'''
    
    # 写入spec文件
    with open('量化股票软件.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ 已创建完整的PyInstaller配置文件: 量化股票软件.spec")

def check_dependencies():
    """检查必要的依赖"""
    required_files = [
        '登录注册.py',
        '股票看图软件_增强版.py',
        '回测系统.py',
        '回测分析.py',
        '策略模板.py',
        '多股票回测系统.py',
        '技术指标库.py',
        '市场数据管理.py',
        '使用者监控.py',
        'token_manager.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
            print("✅ PyInstaller安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    try:
        # 运行PyInstaller
        result = subprocess.run([
            sys.executable, '-m', 'PyInstaller',
            '量化股票软件.spec',
            '--clean',
            '--noconfirm'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ exe文件构建成功!")
            print("📁 输出文件位置: dist/量化股票软件.exe")
            return True
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_readme():
    """创建使用说明"""
    readme_content = """# 量化股票软件使用说明

## 软件介绍
这是一个集成了登录注册和股票分析功能的量化交易软件。

## 主要功能
1. **用户登录注册** - Tushare账户登录和注册
2. **股票数据分析** - 实时股票数据获取和分析
3. **策略回测** - 多种技术指标策略回测
4. **多股票回测** - 支持板块股票批量回测
5. **自定义策略** - 支持用户自定义交易策略
6. **网页交易** - 集成网页交易功能

## 使用方法
1. 双击运行 `量化股票软件.exe`
2. 在登录界面输入手机号进行登录或注册
3. 登录成功后自动启动股票分析软件
4. 选择相应功能进行股票分析和回测

## 注意事项
- 首次使用需要注册Tushare账户
- 确保网络连接稳定
- 大规模回测可能需要较长时间
- 建议在稳定的网络环境下使用

## 技术支持
如有问题请联系技术支持。
"""
    
    with open('README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 已创建使用说明文件: README.md")

def main():
    """主函数"""
    print("🚀 开始完整打包流程...")
    print("=" * 50)
    
    # 检查依赖文件
    if not check_dependencies():
        print("❌ 请确保所有必要文件都存在后再运行")
        return
    
    # 安装PyInstaller
    if not install_pyinstaller():
        print("❌ 无法安装PyInstaller，请手动安装")
        return
    
    # 创建spec文件
    create_complete_spec_file()
    
    # 构建exe
    if build_exe():
        # 创建说明文件
        create_readme()
        print("\n🎉 打包完成!")
        print("📁 可执行文件: dist/量化股票软件.exe")
        print("📖 使用说明: README.md")
    else:
        print("\n❌ 打包失败，请检查错误信息")

if __name__ == "__main__":
    main()
