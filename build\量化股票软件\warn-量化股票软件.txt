
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional), tushare.futures.domestic (optional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), netrc (delayed, conditional), getpass (delayed), http.server (delayed, optional), webbrowser (delayed), psutil (optional), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib (delayed, optional), subprocess (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named fcntl - imported by subprocess (optional), xmlrpc.server (optional), tqdm.utils (delayed, optional), pty (delayed, optional)
missing module named termios - imported by getpass (optional), tty (top-level), tqdm.utils (delayed, optional)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level), numba.testing.main (optional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.freeze_support - imported by multiprocessing (conditional), numba.runtests (conditional)
missing module named multiprocessing.Pool - imported by multiprocessing (delayed, conditional), scipy._lib._util (delayed, conditional)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named 'unittest.mock' - imported by setuptools._distutils.compilers.C.msvc (top-level)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), git.objects.fun (conditional), pydantic._internal._dataclasses (conditional), setuptools._distutils.dist (conditional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named pyimod02_importers - imported by D:\Softwear\PYthone\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), D:\Softwear\PYthone\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional), seaborn.widgets (optional)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed), scipy.linalg._decomp (top-level), scipy.optimize._optimize (top-level), scipy.interpolate._pade (top-level)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level), scipy.stats._stats_py (top-level), scipy.sparse.linalg._isolve.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_schur (top-level), scipy.interpolate._interpolate (top-level), scipy.interpolate._fitpack_impl (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.optimize._tnc (top-level), scipy.optimize._slsqp_py (top-level), scipy.interpolate._fitpack2 (top-level), scipy.integrate._ode (top-level), scipy._lib._finite_differences (top-level), scipy.stats._morestats (top-level), numba.cuda.vectorizers (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), pandas.compat.numpy.function (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._mstats_basic (top-level), scipy.stats._mstats_extras (top-level), pytesseract.pytesseract (optional), seaborn._core.typing (top-level), seaborn._marks.base (top-level), seaborn._stats.density (top-level)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._decomp (top-level), scipy.linalg._decomp_ldl (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level), scipy.stats._morestats (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level), scipy.linalg._matfuncs (top-level), scipy.stats._morestats (top-level)
missing module named pyodide_js - imported by threadpoolctl (delayed, optional)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level), scipy.linalg._decomp (top-level), scipy.linalg._matfuncs (top-level), scipy.optimize._slsqp_py (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
excluded module named unittest - imported by numpy.testing (top-level), doctest (top-level), numpy.testing._private.utils (top-level), Cython.TestUtils (top-level), tornado.util (conditional), tornado.httputil (conditional), numba.testing (top-level), numba.testing.main (top-level), numba.testing.loader (top-level), numba.tests (top-level), numba.tests.support (top-level), numba.cuda.testing (top-level), git.util (delayed, conditional, optional)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy.optimize._lbfgsb_py (top-level), scipy.stats._mstats_extras (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.numpy._typing (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
missing module named StringIO - imported by simplejson.compat (conditional, optional), Cython.Compiler.Annotate (optional), six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named contourpy._contourpy - imported by contourpy (conditional)
missing module named 'contourpy._contourpy' - imported by contourpy (top-level), contourpy.convert (top-level), contourpy.enum_util (top-level), contourpy.typecheck (conditional), contourpy.array (conditional), contourpy.dechunk (top-level)
missing module named markupsafe._speedups - imported by markupsafe (optional)
missing module named railroad - imported by pyparsing.diagram (top-level)
missing module named pyparsing.Word - imported by pyparsing (delayed), pyparsing.unicode (delayed)
missing module named shiboken2 - imported by matplotlib.backends.qt_compat (delayed, conditional, optional)
missing module named sip - imported by matplotlib.backends.qt_compat (delayed, conditional)
excluded module named PyQt5 - imported by matplotlib.backends.qt_compat (delayed, conditional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level)
missing module named gi - imported by matplotlib.cbook (delayed, conditional)
missing module named 'sphinx.ext' - imported by pyarrow.vendored.docscrape (delayed, conditional), seaborn.external.docscrape (delayed, conditional)
missing module named fastcluster - imported by seaborn.matrix (delayed)
missing module named cupy_backends - imported by scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'cupy.cuda' - imported by scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib.array_api_compat.common._helpers (delayed)
missing module named 'jax.experimental' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'jax.numpy' - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named 'dask.array' - imported by scipy._lib.array_api_compat.dask.array (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level), scipy._lib.array_api_compat.common._helpers (delayed, conditional)
missing module named numpy.complex128 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named numpy.complex64 - imported by numpy (top-level), scipy._lib.array_api_compat.numpy._info (top-level), scipy._lib.array_api_compat.dask.array._info (top-level), scipy._lib.array_api_compat.dask.array._aliases (top-level)
missing module named sparse - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy.sparse.linalg._expm_multiply (delayed, conditional), scipy.sparse.linalg._matfuncs (delayed, conditional)
missing module named dask - imported by scipy._lib.array_api_compat.common._helpers (delayed), fsspec.implementations.dask (top-level)
missing module named ndonnx - imported by scipy._lib.array_api_compat.common._helpers (delayed)
excluded module named torch - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.torch (top-level), scipy._lib.array_api_compat.torch._info (top-level), scipy._lib.array_api_compat.torch._aliases (top-level), scipy._lib._array_api (delayed, conditional)
missing module named cupy - imported by scipy._lib.array_api_compat.common._helpers (delayed, conditional), scipy._lib.array_api_compat.cupy (top-level), scipy._lib.array_api_compat.cupy._aliases (top-level), scipy._lib.array_api_compat.cupy._info (top-level), scipy._lib.array_api_compat.cupy._typing (top-level), scipy._lib._array_api (delayed, conditional)
missing module named jax - imported by scipy._lib.array_api_compat.common._helpers (delayed), scipy._lib._array_api (delayed, conditional)
missing module named 'numpy.lib.array_utils' - imported by scipy._lib.array_api_compat.common._linalg (conditional)
missing module named 'numpy.linalg._linalg' - imported by scipy._lib.array_api_compat.numpy.linalg (delayed, optional)
excluded module named pytest - imported by scipy._lib._testutils (delayed), pandas._testing._io (delayed), pandas._testing (delayed), fsspec.conftest (top-level), pyarrow.conftest (top-level), pyarrow.tests.util (top-level)
missing module named __builtin__ - imported by Cython.Shadow (optional), Cython.Compiler.Errors (optional), Cython.Compiler.Main (optional), Cython.Compiler.Symtab (optional), Cython.Compiler.ExprNodes (optional), Cython.Compiler.Optimize (optional), Cython.Distutils.build_ext (optional), Cython.Build.Inline (delayed, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named pythran - imported by Cython.Build.Dependencies (optional), Cython.Compiler.Pythran (optional)
missing module named collections.Iterable - imported by collections (optional), Cython.Build.Dependencies (optional)
missing module named Cython.Parser - imported by Cython.Compiler.Main (delayed, conditional, optional)
missing module named pygments.lexers.CppLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.CythonLexer - imported by pygments.lexers (delayed, optional), Cython.Compiler.Annotate (delayed, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.lexers.LlvmLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pygments.lexers.GasLexer - imported by pygments.lexers (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional)
missing module named _winreg - imported by platform (delayed, optional), selenium.webdriver.firefox.firefox_binary (delayed, optional), pygments.formatters.img (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named pygments.formatters.HtmlFormatter - imported by pygments.formatters (delayed, optional), Cython.Compiler.Annotate (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.TerminalFormatter - imported by pygments.formatters (delayed, optional), numba.core.annotations.pretty_annotate (delayed, optional)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (delayed, conditional, optional), numba.core.ir (delayed, conditional, optional), numba.core.codegen (delayed, conditional, optional), numba.core.utils (delayed, conditional, optional)
missing module named pyximport.test - imported by pyximport (conditional), pyximport.pyxbuild (conditional)
missing module named 'IPython.utils' - imported by Cython.Build.IpythonMagic (top-level)
missing module named 'IPython.paths' - imported by Cython.Build.IpythonMagic (optional)
missing module named 'IPython.core' - imported by dotenv.ipython (top-level), Cython.Build.IpythonMagic (top-level), pandas.io.formats.printing (delayed, conditional), rich.pretty (delayed, optional)
missing module named sphinx - imported by scipy._lib._docscrape (delayed, conditional)
missing module named scipy.special.gammaincinv - imported by scipy.special (top-level), scipy.stats._qmvnt (top-level)
missing module named scipy.special.ive - imported by scipy.special (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.betaln - imported by scipy.special (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.special.beta - imported by scipy.special (top-level), scipy.stats._tukeylambda_stats (top-level)
missing module named scipy.special.loggamma - imported by scipy.special (top-level), scipy.fft._fftlog_backend (top-level), scipy.stats._multivariate (top-level)
missing module named scipy.linalg.qr_insert - imported by scipy.linalg (top-level), scipy.sparse.linalg._isolve._gcrotmk (top-level)
missing module named scipy.sparse.csc_matrix - imported by scipy.sparse (top-level), scipy.linalg._sketches (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._trustregion_constr.qp_subproblem (top-level), scipy.optimize._linprog_highs (top-level), scipy.integrate._bvp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named 'scipy.optimize._highspy._core.simplex_constants' - imported by scipy.optimize._linprog_highs (top-level)
missing module named scipy.sparse.issparse - imported by scipy.sparse (delayed), scipy._lib._array_api (delayed), scipy.sparse.linalg._interface (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._eigen.lobpcg.lobpcg (top-level), scipy.sparse.linalg._norm (top-level), scipy.sparse.csgraph._laplacian (top-level), scipy.optimize._numdiff (top-level), scipy.optimize._constraints (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.optimize._lsq.least_squares (top-level), scipy.optimize._lsq.common (top-level), scipy.optimize._lsq.lsq_linear (top-level), scipy.optimize._linprog_highs (top-level), scipy.optimize._differentialevolution (top-level), scipy.optimize._milp (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level), pandas.core.dtypes.common (delayed, conditional, optional)
missing module named scipy.sparse.csc_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.optimize._milp (top-level)
missing module named scipy.interpolate.PPoly - imported by scipy.interpolate (top-level), scipy.interpolate._cubic (top-level), scipy.spatial.transform._rotation_spline (delayed), scipy.integrate._bvp (delayed)
missing module named scipy.sparse.csr_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level), scipy.interpolate._bsplines (top-level), scipy.interpolate._ndbspline (top-level)
missing module named scipy.sparse.diags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.spdiags - imported by scipy.sparse (delayed), scipy.sparse.linalg._special_sparse_arrays (delayed)
missing module named scipy.sparse.dia_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scipy.sparse.eye - imported by scipy.sparse (top-level), scipy.sparse.linalg._eigen.arpack.arpack (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level), scipy.optimize._trustregion_constr.equality_constrained_sqp (top-level), scipy.optimize._trustregion_constr.projections (top-level), scipy.integrate._ivp.bdf (top-level), scipy.integrate._ivp.radau (top-level)
missing module named scipy.sparse.kron - imported by scipy.sparse (top-level), scipy.sparse.linalg._special_sparse_arrays (top-level)
missing module named scikits - imported by scipy.sparse.linalg._dsolve.linsolve (optional)
missing module named scipy.sparse.diags_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.eye_array - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.SparseEfficiencyWarning - imported by scipy.sparse (top-level), scipy.sparse.linalg._dsolve.linsolve (top-level)
missing module named scipy.sparse.linalg.onenormest - imported by scipy.sparse.linalg (top-level), scipy.linalg._matfuncs_inv_ssq (top-level)
missing module named scipy.spatial.Voronoi - imported by scipy.spatial (top-level), scipy.stats._qmc (top-level)
missing module named 'scikits.umfpack' - imported by scipy.optimize._linprog_ip (optional)
missing module named 'sksparse.cholmod' - imported by scipy.optimize._linprog_ip (optional)
missing module named sksparse - imported by scipy.optimize._trustregion_constr.projections (optional), scipy.optimize._linprog_ip (optional)
missing module named numpy.inexact - imported by numpy (top-level), scipy.linalg._decomp (top-level), scipy.special._basic (top-level), scipy.optimize._minpack_py (top-level)
missing module named numpy.greater - imported by numpy (top-level), scipy.optimize._minpack_py (top-level)
excluded module named scipy.sparse.csgraph._validation - imported by scipy.sparse.csgraph (top-level)
missing module named uarray - imported by scipy._lib.uarray (conditional, optional)
missing module named numpy.power - imported by numpy (top-level), scipy.stats._kde (top-level), seaborn.external.kde (top-level)
missing module named numpy.logical_and - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level)
missing module named numpy.log - imported by numpy (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level), scipy.stats._morestats (top-level)
missing module named scipy.stats.iqr - imported by scipy.stats (delayed), scipy.stats._hypotests (delayed)
missing module named numpy.sinh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.cosh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.tanh - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.expm1 - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.log1p - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.ceil - imported by numpy (top-level), scipy.stats._discrete_distns (top-level)
missing module named numpy.sign - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.conjugate - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.logical_not - imported by numpy (top-level), scipy.linalg._matfuncs (top-level)
missing module named numpy.single - imported by numpy (top-level), scipy.linalg._decomp_schur (top-level)
missing module named numpy.conj - imported by numpy (top-level), scipy.linalg._decomp (top-level)
missing module named numpy.arcsin - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level)
missing module named scipy.linalg._flapack_64 - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._clapack - imported by scipy.linalg (optional), scipy.linalg.lapack (optional)
missing module named scipy.linalg._fblas_64 - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named scipy.linalg._cblas - imported by scipy.linalg (optional), scipy.linalg.blas (optional)
missing module named numpy.arccos - imported by numpy (top-level), scipy.linalg._decomp_svd (top-level), scipy.special._orthogonal (top-level)
missing module named numpy.floor - imported by numpy (top-level), scipy.special._basic (top-level), scipy.special._orthogonal (top-level), scipy.stats._distn_infrastructure (top-level), scipy.stats._discrete_distns (top-level)
missing module named scipy.special.airy - imported by scipy.special (top-level), scipy.special._orthogonal (top-level)
missing module named collections.Callable - imported by collections (optional), socks (optional), cffi.api (optional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cStringIO - imported by simplejson.compat (conditional, optional), cffi.ffiplatform (optional), xlrd.timemachine (conditional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named scipy._distributor_init_local - imported by scipy (optional), scipy._distributor_init (optional)
missing module named 'yaml._yaml' - imported by yaml.cyaml (top-level)
missing module named cupyx - imported by scipy._lib._array_api (delayed, conditional)
missing module named 'statsmodels.nonparametric' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.tools' - imported by seaborn.regression (delayed)
missing module named 'statsmodels.robust' - imported by seaborn.regression (delayed, conditional)
missing module named 'statsmodels.genmod' - imported by seaborn.regression (delayed, conditional)
missing module named statsmodels - imported by seaborn.regression (optional)
missing module named 'com.sun' - imported by numba.misc.appdirs (delayed, conditional, optional), seaborn.external.appdirs (delayed, conditional, optional)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named org - imported by pickle (optional)
missing module named xmlrpclib - imported by defusedxml.xmlrpc (conditional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), google.auth.transport._mtls_helper (delayed), google.auth.transport.requests (delayed, optional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named collections.MutableMapping - imported by collections (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), google.auth.jwt (optional), google.auth.pluggable (optional), google.auth.identity_pool (optional), html5lib._utils (optional), html5lib._trie._base (optional)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by tushare.stock.fundamental (optional), tushare.stock.trading (optional), tushare.stock.macro (optional), tushare.util.netbase (optional), tushare.stock.classifying (optional), tushare.stock.newsevent (optional), tushare.stock.reference (optional), tushare.stock.billboard (optional), tushare.internet.boxoffice (optional), tushare.internet.indexes (optional), tushare.fund.nav (optional), tushare.futures.intlfutures (optional), tushare.stock.globals (optional), tushare.futures.domestic (optional), tushare.coins.market (optional), lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named UserDict - imported by simplejson.ordered_dict (top-level), pytz.lazy (optional)
missing module named 'IPython.display' - imported by llvmlite.binding.analysis (delayed, conditional, optional), tqdm.notebook (conditional, optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by pyarrow (optional), matplotlib (delayed, conditional, optional), tqdm.version (optional)
missing module named 'pandas.api.internals' - imported by pyarrow.pandas_compat (delayed, conditional)
missing module named 'pyarrow._cuda' - imported by pyarrow.cuda (top-level)
missing module named 'pyarrow.gandiva' - imported by pyarrow.conftest (optional)
missing module named fastparquet - imported by fsspec.parquet (delayed), pyarrow.conftest (optional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named uvloop - imported by aiohttp.worker (delayed)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'pyarrow._azurefs' - imported by pyarrow.fs (optional)
missing module named 'setuptools_scm.git' - imported by pyarrow (delayed, optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named sets - imported by pytz.tzinfo (optional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named 'sqlalchemy.engine' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.types' - imported by pandas.io.sql (delayed, conditional)
missing module named 'sqlalchemy.schema' - imported by pandas.io.sql (delayed)
missing module named 'sqlalchemy.sql' - imported by pandas.io.sql (conditional)
missing module named sqlalchemy - imported by pandas.io.sql (delayed, conditional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyu2f.model' - imported by google.oauth2.challenges (delayed, optional)
missing module named 'pyu2f.errors' - imported by google.oauth2.challenges (delayed, optional)
missing module named pyu2f - imported by google.oauth2.challenges (delayed, optional)
missing module named 'requests.packages.urllib3' - imported by google.auth.transport.requests (top-level)
missing module named 'google.appengine' - imported by google.auth.app_engine (optional)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named numba.typed.Dict - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.typed.dictobject (delayed, conditional), numba.typed.dictimpl (delayed), pandas.core._numba.extensions (delayed)
missing module named llvmlite.ir.Constant - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed), numba.core.base (top-level), numba.core.pythonapi (top-level), numba.core.lowering (top-level), numba.core.generators (top-level), numba.core.callwrapper (top-level), numba.pycc.llvm_types (top-level), numba.np.npdatetime (top-level), numba.np.math.mathimpl (top-level), numba.np.math.numbers (top-level), numba.cpython.unicode (top-level), numba.np.arrayobj (top-level), numba.np.ufunc.wrappers (top-level), numba.cpython.old_mathimpl (top-level), numba.cpython.old_numbers (top-level), numba.cpython.new_mathimpl (top-level), numba.cpython.new_numbers (top-level)
missing module named llvmlite.binding.parse_assembly - imported by llvmlite.binding (delayed), llvmlite.ir.types (delayed)
missing module named graphviz - imported by llvmlite.binding.analysis (delayed), numba.core.ir (delayed, optional), numba.core.controlflow (delayed, optional), numba.misc.inspection (delayed, optional), numba.core.codegen (delayed)
missing module named llvmlite.ir.Value - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.GlobalVariable - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named llvmlite.ir.Module - imported by llvmlite.ir (delayed), llvmlite.ir.types (delayed)
missing module named numba.core.types.StringLiteral - imported by numba.core.types (top-level), numba.np.arrayobj (top-level)
missing module named numba.core.types.Tuple - imported by numba.core.types (delayed), numba.core.types.iterators (delayed), numba.core.types.npytypes (delayed)
missing module named numba.core.types.Array - imported by numba.core.types (delayed), numba.core.types.abstract (delayed)
missing module named numba.core.types.WrapperAddressProtocol - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionPrototype - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.UndefinedFunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.FunctionType - imported by numba.core.types (top-level), numba.experimental.function_type (top-level)
missing module named numba.core.types.ClassInstanceType - imported by numba.core.types (top-level), numba.experimental.jitclass.overloads (top-level)
missing module named coverage - imported by numba.misc.coverage_support (optional), numba.tests.support (optional)
missing module named 'elftools.elf' - imported by numba.core.codegen (delayed)
missing module named elftools - imported by numba.core.codegen (delayed)
missing module named r2pipe - imported by numba.misc.inspection (delayed, optional)
missing module named com - imported by numba.misc.appdirs (delayed)
missing module named numba.types.uint64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level)
missing module named numba.types.int64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float64 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.float32 - imported by numba.types (top-level), numba.cuda.mathimpl (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named ptxcompiler - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cubinlinker - imported by numba.cuda.cudadrv.driver (delayed, optional), numba.cuda.testing (delayed, optional)
missing module named cuda - imported by numba.core.config (delayed, conditional, optional), numba.cuda.cudadrv.driver (conditional)
missing module named numba.types.Tuple - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.void - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int32 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named numba.types.int16 - imported by numba.types (top-level), numba.cuda.libdevicefuncs (top-level)
missing module named 'unittest.suite' - imported by numba.tests (top-level)
missing module named xmlrunner - imported by numba.testing (delayed, conditional)
missing module named gitdb_speedups - imported by gitdb.fun (optional)
missing module named 'gitdb_speedups._perf' - imported by gitdb.stream (optional), gitdb.pack (optional)
missing module named sha - imported by gitdb.util (delayed, optional)
missing module named Queue - imported by numba.testing.main (optional)
missing module named numba.cuda.is_available - imported by numba.cuda (delayed), numba.cuda.testing (delayed)
missing module named numba.core.types.NoneType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.Type - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.ListTypeIteratorType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListTypeIterableType - imported by numba.core.types (top-level), numba.typed.listobject (top-level)
missing module named numba.core.types.ListType - imported by numba.core.types (top-level), numba.typed.listobject (top-level), numba.typed.typedlist (top-level)
missing module named numba.core.types.DictIteratorType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictValuesIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictKeysIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictItemsIterableType - imported by numba.core.types (top-level), numba.typed.dictobject (top-level)
missing module named numba.core.types.DictType - imported by numba.core.types (top-level), numba.typed.typeddict (top-level), numba.typed.dictobject (top-level)
missing module named numba.typed.List - imported by numba.typed (delayed), numba.core.typing.typeof (delayed), numba.core.codegen (delayed), numba.typed.listobject (delayed)
excluded module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named pandas.compat.StringIO - imported by pandas.compat (conditional), tushare.stock.fundamental (conditional), tushare.stock.trading (conditional), tushare.stock.reference (conditional), tushare.stock.shibor (conditional), tushare.stock.billboard (conditional), tushare.internet.indexes (conditional)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pytdx.exhq' - imported by tushare.util.conns (delayed)
missing module named pytdx - imported by tushare.util.conns (delayed)
missing module named httplib - imported by tushare.futures.domestic (optional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic._internal._core_utils (delayed), pydantic.deprecated.copy_internals (delayed, conditional), tushare.subs.model.tick (top-level)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named wsaccel - imported by websocket._utils (optional)
missing module named 'python_socks._types' - imported by websocket._http (optional)
missing module named 'python_socks._errors' - imported by websocket._http (optional)
missing module named python_socks - imported by websocket._http (optional)
missing module named 'wsaccel.xormask' - imported by websocket._abnf (optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
