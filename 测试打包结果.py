#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包结果脚本
验证打包后的exe文件功能完整性
"""

import os
import sys
import subprocess
import time
import psutil
from pathlib import Path

def check_exe_exists():
    """检查exe文件是否存在"""
    exe_path = Path("dist/量化股票软件.exe")
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
        print(f"✅ exe文件存在: {exe_path}")
        print(f"📏 文件大小: {file_size:.1f} MB")
        return True
    else:
        print("❌ exe文件不存在")
        return False

def test_exe_startup():
    """测试exe文件启动"""
    print("🚀 测试exe文件启动...")
    exe_path = "dist/量化股票软件.exe"
    
    try:
        # 启动exe文件
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        print("⏳ 等待程序启动...")
        time.sleep(5)  # 等待5秒让程序启动
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 程序启动成功，正在运行")
            
            # 等待一段时间观察程序运行
            time.sleep(3)
            
            # 检查是否有窗口进程
            found_window = False
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if '量化股票软件' in proc.info['name']:
                        found_window = True
                        break
                except:
                    pass
            
            if found_window:
                print("✅ 检测到程序窗口进程")
            else:
                print("⚠️ 未检测到程序窗口进程")
            
            # 终止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ 程序正常终止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ 程序被强制终止")
            
            return True
        else:
            # 程序已退出，获取错误信息
            stdout, stderr = process.communicate()
            print("❌ 程序启动后立即退出")
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def test_dependencies():
    """测试依赖文件"""
    print("📦 检查依赖文件...")
    
    required_modules = [
        'token_manager.py',
        '股票看图软件_增强版.py',
        '回测系统.py',
        '市场数据管理.py',
        '使用者监控.py'
    ]
    
    missing_modules = []
    for module in required_modules:
        if not os.path.exists(module):
            missing_modules.append(module)
    
    if missing_modules:
        print("⚠️ 缺少依赖模块:")
        for module in missing_modules:
            print(f"   - {module}")
        return False
    else:
        print("✅ 所有依赖模块都存在")
        return True

def test_import_modules():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    test_modules = [
        ('tkinter', 'GUI库'),
        ('pandas', '数据处理库'),
        ('numpy', '数值计算库'),
        ('matplotlib', '图表库'),
        ('requests', '网络请求库'),
        ('selenium', '浏览器自动化库'),
        ('PIL', '图像处理库'),
        ('openpyxl', 'Excel处理库'),
    ]
    
    failed_imports = []
    for module_name, description in test_modules:
        try:
            __import__(module_name)
            print(f"✅ {description} ({module_name})")
        except ImportError:
            print(f"❌ {description} ({module_name})")
            failed_imports.append(module_name)
    
    if failed_imports:
        print(f"⚠️ {len(failed_imports)} 个模块导入失败")
        return False
    else:
        print("✅ 所有测试模块导入成功")
        return True

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    expected_files = [
        'dist/量化股票软件.exe',
        'README.md',
        '量化股票软件.spec',
        '完整打包脚本.py',
        '一键打包.bat'
    ]
    
    missing_files = []
    for file_path in expected_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print("⚠️ 缺少文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 文件结构完整")
        return True

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*50)
    print("📊 测试报告")
    print("="*50)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    print(f"总测试项: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！exe文件功能完整")
        print("💡 建议:")
        print("   1. 可以分发给用户使用")
        print("   2. 建议在不同环境下进一步测试")
        print("   3. 确保目标机器有必要的运行时环境")
    else:
        print("\n⚠️ 部分测试失败，建议检查:")
        print("   1. 检查缺失的依赖")
        print("   2. 重新打包")
        print("   3. 在目标环境测试")

def main():
    """主测试函数"""
    print("🧪 开始测试打包结果...")
    print("="*50)
    
    # 执行各项测试
    test_results = {}
    
    test_results["文件结构检查"] = check_file_structure()
    test_results["exe文件存在"] = check_exe_exists()
    test_results["依赖文件检查"] = test_dependencies()
    test_results["模块导入测试"] = test_import_modules()
    test_results["exe启动测试"] = test_exe_startup()
    
    # 生成测试报告
    generate_test_report(test_results)

if __name__ == "__main__":
    main()
