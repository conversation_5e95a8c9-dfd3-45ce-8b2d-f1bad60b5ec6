import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import tushare as ts
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
from datetime import datetime
import threading
import json
import time
from datetime import datetime

# 导入回测系统
from 回测系统 import BacktestEngine, MACDStrategy, KDJStrategy, CustomStrategy
from 回测分析 import BacktestAnalyzer
from 策略模板 import STRATEGY_TEMPLATES, get_template_names, STRATEGY_GUIDE
from 多股票回测系统 import MultiStockBacktestEngine, MultiStockAnalyzer

# 导入网页交易相关库
try:
    from selenium import webdriver
    from selenium.webdriver.edge.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.edge.options import Options
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("警告: 未安装selenium，网页交易功能不可用")

# 导入技术指标库
try:
    import 技术指标库 as ta
except ImportError:
    print("警告：技术指标库未找到，部分功能可能无法使用")
    ta = None

# 导入市场数据管理模块
try:
    from 市场数据管理 import MarketDataManager
    MARKET_DATA_AVAILABLE = True
except ImportError:
    print("警告：市场数据管理模块未找到，市场数据功能不可用")
    MARKET_DATA_AVAILABLE = False

class EnhancedStockViewerApp:
    def __init__(self, master):
        self.master = master
        master.title("股票看图软件 - 增强版 (含回测功能)")
        master.geometry("1200x800")
        
        # 读取Tushare Token
        token = self.load_tushare_token()

        # 设置Tushare API
        ts.set_token(token)
        self.pro = ts.pro_api()

        # 初始化市场数据管理器
        if MARKET_DATA_AVAILABLE:
            self.market_data_manager = MarketDataManager(
                cache_dir="market_data_cache",
                token=token
            )
            # 启动数据预加载
            self.market_data_manager.start_preload()
        else:
            self.market_data_manager = None

        # 数据存储
        self.df = None
        self.backtest_results = None
        self.custom_strategy_code = ""

        # 实时数据相关
        self.realtime_data = None
        self.realtime_thread = None
        self.realtime_running = False
        self.realtime_interval = 30  # 默认30秒更新一次，提高实时性
        self.last_update_time = None
        self.last_price = None  # 记录上次价格，用于比较变化
        
        # 创建界面组件
        self.create_widgets()

        # 绑定窗口关闭事件
        master.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 初始化使用者监控
        self.init_user_monitor()

    def init_user_monitor(self):
        """初始化使用者监控系统"""
        try:
            from 使用者监控 import get_monitor
            self.user_monitor = get_monitor()
            print("使用者监控系统初始化成功")
        except Exception as e:
            print(f"使用者监控系统初始化失败: {e}")
            self.user_monitor = None

    def monitor_backtest_completion(self, results):
        """监控回测完成事件"""
        try:
            if self.user_monitor is None:
                return

            # 获取自定义策略代码
            custom_code = getattr(self, 'custom_strategy_code', '')

            # 调用监控系统处理回测完成事件
            success = self.user_monitor.process_backtest_completion(results, custom_code)

            if success:
                print("回测数据已成功上传到监控系统")
                # 在结果文本中显示上传成功信息
                self.result_text.insert(tk.END, "\n✅ 回测数据已自动上传到监控系统\n")
            else:
                print("回测数据上传失败")
                self.result_text.insert(tk.END, "\n⚠️ 回测数据上传失败，请检查网络连接\n")

        except Exception as e:
            print(f"监控回测完成事件失败: {e}")
            self.result_text.insert(tk.END, f"\n❌ 监控系统错误: {e}\n")

    def monitor_multi_backtest_completion(self, results):
        """监控多股票回测完成事件"""
        try:
            if self.user_monitor is None:
                return

            # 获取自定义策略代码
            custom_code = getattr(self, 'custom_strategy_code', '')

            # 将多股票回测结果转换为单一结果格式
            portfolio_results = results.get('portfolio_results', {})

            # 调用监控系统处理回测完成事件
            success = self.user_monitor.process_backtest_completion(portfolio_results, custom_code)

            if success:
                print("多股票回测数据已成功上传到监控系统")
                # 在结果文本中显示上传成功信息
                self.multi_result_text.insert(tk.END, "\n✅ 回测数据已自动上传到监控系统\n")
            else:
                print("多股票回测数据上传失败")
                self.multi_result_text.insert(tk.END, "\n⚠️ 回测数据上传失败，请检查网络连接\n")

        except Exception as e:
            print(f"监控多股票回测完成事件失败: {e}")
            self.multi_result_text.insert(tk.END, f"\n❌ 监控系统错误: {e}\n")

    def load_tushare_token(self):
        """从内存或文件中读取Tushare Token"""
        import os

        # 优先从token管理器读取token（内存中）
        try:
            from token_manager import get_token, is_token_valid
            if is_token_valid():
                token = get_token()
                if token:
                    print(f"成功从内存读取Tushare Token: {token[:10]}...")
                    return token
        except ImportError:
            print("Token管理器不可用，尝试从文件读取")

        # 尝试从登录注册程序保存的token文件中读取（兼容性）
        token_file = os.path.join(os.path.dirname(__file__), "tushare_token.txt")

        if os.path.exists(token_file):
            try:
                with open(token_file, 'r', encoding='utf-8') as f:
                    token = f.read().strip()
                if token and len(token) > 10:  # 验证token长度
                    print(f"成功从文件读取Tushare Token: {token[:10]}...")
                    return token
                else:
                    print("Token文件存在但内容无效")
            except Exception as e:
                print(f"读取Token文件失败: {e}")
        else:
            print("未找到Token文件: tushare_token.txt")

        # 如果没有找到token文件或读取失败，返回空字符串
        print("警告: 未找到有效的Tushare Token，某些功能可能无法使用")
        return ''

    def create_widgets(self):
        # 创建主框架
        main_frame = tk.Frame(self.master)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建选项卡
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 股票分析选项卡
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text="股票分析")
        
        # 回测选项卡
        self.backtest_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.backtest_frame, text="策略回测")

        # 自定义策略选项卡
        self.custom_strategy_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.custom_strategy_frame, text="自定义策略")

        # 多股票回测选项卡
        self.multi_stock_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.multi_stock_frame, text="多股票回测")

        # 网页交易选项卡
        self.web_trading_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.web_trading_frame, text="网页交易")

        # 市场数据管理选项卡
        if MARKET_DATA_AVAILABLE:
            self.market_data_frame = ttk.Frame(self.notebook)
            self.notebook.add(self.market_data_frame, text="市场数据")

        # 创建股票分析界面
        self.create_analysis_widgets()

        # 创建回测界面
        self.create_backtest_widgets()

        # 创建自定义策略界面
        self.create_custom_strategy_widgets()

        # 创建多股票回测界面
        self.create_multi_stock_widgets()

        # 创建网页交易界面
        self.create_web_trading_widgets()

        # 创建市场数据管理界面
        if MARKET_DATA_AVAILABLE:
            self.create_market_data_widgets()
        
    def create_analysis_widgets(self):
        # 输入框区域
        input_frame = tk.Frame(self.analysis_frame)
        input_frame.pack(pady=10)
        
        tk.Label(input_frame, text="股票代码:").grid(row=0, column=0, padx=5)
        self.code_entry = tk.Entry(input_frame, width=15)
        self.code_entry.grid(row=0, column=1, padx=5)
        self.code_entry.insert(0, "000001.SZ")
        
        tk.Label(input_frame, text="开始日期:").grid(row=0, column=2, padx=5)
        self.start_date_entry = tk.Entry(input_frame, width=10)
        self.start_date_entry.grid(row=0, column=3, padx=5)
        self.start_date_entry.insert(0, "20230101")
        
        tk.Label(input_frame, text="结束日期:").grid(row=0, column=4, padx=5)
        self.end_date_entry = tk.Entry(input_frame, width=10)
        self.end_date_entry.grid(row=0, column=5, padx=5)
        self.end_date_entry.insert(0, datetime.now().strftime("%Y%m%d"))
        
        # 查询按钮
        self.query_button = tk.Button(input_frame, text="查询", command=self.query_stock)
        self.query_button.grid(row=0, column=6, padx=10)

        # 实时数据控制区域
        realtime_frame = tk.Frame(self.analysis_frame)
        realtime_frame.pack(pady=5)

        # 实时数据开关
        self.realtime_var = tk.BooleanVar(value=False)
        self.realtime_check = tk.Checkbutton(realtime_frame, text="启用实时数据",
                                           variable=self.realtime_var,
                                           command=self.toggle_realtime_data)
        self.realtime_check.pack(side=tk.LEFT, padx=5)

        # 更新间隔设置
        tk.Label(realtime_frame, text="更新间隔(秒):").pack(side=tk.LEFT, padx=5)
        self.realtime_interval_entry = tk.Entry(realtime_frame, width=8)
        self.realtime_interval_entry.pack(side=tk.LEFT, padx=5)
        self.realtime_interval_entry.insert(0, "30")  # 默认30秒，提高实时性

        # 强制刷新按钮
        self.refresh_button = tk.Button(realtime_frame, text="立即刷新",
                                      command=self.force_refresh_realtime_data)
        self.refresh_button.pack(side=tk.LEFT, padx=5)

        # 实时状态显示
        self.realtime_status_label = tk.Label(realtime_frame, text="实时数据: 未启动", fg='gray')
        self.realtime_status_label.pack(side=tk.LEFT, padx=10)

        # 最后更新时间显示
        self.last_update_label = tk.Label(realtime_frame, text="", fg='blue')
        self.last_update_label.pack(side=tk.LEFT, padx=10)

        # 价格变化显示
        self.price_change_label = tk.Label(realtime_frame, text="", fg='black')
        self.price_change_label.pack(side=tk.LEFT, padx=10)
        
        # 指标选择区域
        indicator_frame = tk.Frame(self.analysis_frame)
        indicator_frame.pack(pady=5)
        
        self.indicator_var = tk.StringVar(value="MACD")
        tk.Radiobutton(indicator_frame, text="MACD", variable=self.indicator_var,
                      value="MACD", command=self.update_chart).pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(indicator_frame, text="KDJ", variable=self.indicator_var,
                      value="KDJ", command=self.update_chart).pack(side=tk.LEFT, padx=10)
        tk.Radiobutton(indicator_frame, text="缠论", variable=self.indicator_var,
                      value="CHANLUN", command=self.update_chart).pack(side=tk.LEFT, padx=10)
        
        # 图表区域
        self.figure = Figure(figsize=(10, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.figure, master=self.analysis_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 添加工具栏以支持缩放和移动
        from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
        self.toolbar = NavigationToolbar2Tk(self.canvas, self.analysis_frame)
        self.toolbar.update()

        # 初始化视图控制变量
        self.init_view_control()

        # 绑定键盘事件
        self.setup_keyboard_navigation()

        # 添加策略选择和控制面板
        strategy_control_frame = tk.Frame(self.analysis_frame)
        strategy_control_frame.pack(fill=tk.X, pady=5)

        # 策略选择区域
        strategy_select_frame = tk.LabelFrame(strategy_control_frame, text="策略选择", padx=10, pady=5)
        strategy_select_frame.pack(side=tk.LEFT, padx=5)

        self.chart_strategy_var = tk.StringVar(value="NONE")
        tk.Radiobutton(strategy_select_frame, text="无策略", variable=self.chart_strategy_var,
                      value="NONE", command=self.update_chart_strategy).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(strategy_select_frame, text="MACD策略", variable=self.chart_strategy_var,
                      value="MACD", command=self.update_chart_strategy).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(strategy_select_frame, text="KDJ策略", variable=self.chart_strategy_var,
                      value="KDJ", command=self.update_chart_strategy).pack(side=tk.LEFT, padx=5)
        tk.Radiobutton(strategy_select_frame, text="自定义策略", variable=self.chart_strategy_var,
                      value="CUSTOM", command=self.update_chart_strategy).pack(side=tk.LEFT, padx=5)

        # 控制按钮区域
        control_frame = tk.Frame(strategy_control_frame)
        control_frame.pack(side=tk.RIGHT, padx=5)

        self.show_signals_var = tk.BooleanVar(value=False)
        self.show_signals_check = tk.Checkbutton(control_frame, text="显示买卖点",
                                                variable=self.show_signals_var,
                                                command=self.update_chart_with_signals)
        self.show_signals_check.pack(side=tk.LEFT, padx=5)

        tk.Button(control_frame, text="重置视图", command=self.reset_chart_view).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="运行策略", command=self.run_chart_strategy,
                 bg='lightgreen').pack(side=tk.LEFT, padx=5)

        # 十字光标控制
        self.crosshair_var = tk.BooleanVar(value=True)
        self.crosshair_check = tk.Checkbutton(control_frame, text="十字光标",
                                            variable=self.crosshair_var,
                                            command=self.toggle_crosshair)
        self.crosshair_check.pack(side=tk.LEFT, padx=5)

        # 添加视图控制说明
        view_info_frame = tk.Frame(strategy_control_frame)
        view_info_frame.pack(side=tk.RIGHT, padx=10)
        tk.Label(view_info_frame, text="导航: ←→移动 ↑↓缩放 Home/End快速定位 | 鼠标悬停显示K线信息",
                font=('Arial', 9), fg='blue').pack()
        
    def create_backtest_widgets(self):
        # 左侧参数设置区域
        left_frame = tk.Frame(self.backtest_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10, pady=10)
        
        # 回测参数设置
        params_frame = tk.LabelFrame(left_frame, text="回测参数", padx=10, pady=10)
        params_frame.pack(fill=tk.X, pady=5)
        
        # 股票代码
        tk.Label(params_frame, text="股票代码:").grid(row=0, column=0, sticky='w', pady=2)
        self.bt_code_entry = tk.Entry(params_frame, width=15)
        self.bt_code_entry.grid(row=0, column=1, pady=2)
        self.bt_code_entry.insert(0, "000001.SZ")
        
        # 日期范围
        tk.Label(params_frame, text="开始日期:").grid(row=1, column=0, sticky='w', pady=2)
        self.bt_start_date_entry = tk.Entry(params_frame, width=15)
        self.bt_start_date_entry.grid(row=1, column=1, pady=2)
        self.bt_start_date_entry.insert(0, "20230101")
        
        tk.Label(params_frame, text="结束日期:").grid(row=2, column=0, sticky='w', pady=2)
        self.bt_end_date_entry = tk.Entry(params_frame, width=15)
        self.bt_end_date_entry.grid(row=2, column=1, pady=2)
        self.bt_end_date_entry.insert(0, datetime.now().strftime("%Y%m%d"))
        
        # 初始资金
        tk.Label(params_frame, text="初始资金:").grid(row=3, column=0, sticky='w', pady=2)
        self.initial_capital_entry = tk.Entry(params_frame, width=15)
        self.initial_capital_entry.grid(row=3, column=1, pady=2)
        self.initial_capital_entry.insert(0, "100000")
        
        # 手续费率
        tk.Label(params_frame, text="手续费率:").grid(row=4, column=0, sticky='w', pady=2)
        self.commission_entry = tk.Entry(params_frame, width=15)
        self.commission_entry.grid(row=4, column=1, pady=2)
        self.commission_entry.insert(0, "0.001")
        
        # 策略选择
        strategy_frame = tk.LabelFrame(left_frame, text="策略选择", padx=10, pady=10)
        strategy_frame.pack(fill=tk.X, pady=5)
        
        self.strategy_var = tk.StringVar(value="MACD")
        tk.Radiobutton(strategy_frame, text="MACD策略", variable=self.strategy_var,
                      value="MACD").pack(anchor='w')
        tk.Radiobutton(strategy_frame, text="KDJ策略", variable=self.strategy_var,
                      value="KDJ").pack(anchor='w')
        tk.Radiobutton(strategy_frame, text="自定义策略", variable=self.strategy_var,
                      value="CUSTOM").pack(anchor='w')
        
        # MACD参数
        macd_frame = tk.LabelFrame(left_frame, text="MACD参数", padx=10, pady=10)
        macd_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(macd_frame, text="快线周期:").grid(row=0, column=0, sticky='w')
        self.macd_fast_entry = tk.Entry(macd_frame, width=10)
        self.macd_fast_entry.grid(row=0, column=1)
        self.macd_fast_entry.insert(0, "12")
        
        tk.Label(macd_frame, text="慢线周期:").grid(row=1, column=0, sticky='w')
        self.macd_slow_entry = tk.Entry(macd_frame, width=10)
        self.macd_slow_entry.grid(row=1, column=1)
        self.macd_slow_entry.insert(0, "26")
        
        tk.Label(macd_frame, text="信号周期:").grid(row=2, column=0, sticky='w')
        self.macd_signal_entry = tk.Entry(macd_frame, width=10)
        self.macd_signal_entry.grid(row=2, column=1)
        self.macd_signal_entry.insert(0, "9")
        
        # KDJ参数
        kdj_frame = tk.LabelFrame(left_frame, text="KDJ参数", padx=10, pady=10)
        kdj_frame.pack(fill=tk.X, pady=5)
        
        tk.Label(kdj_frame, text="K周期:").grid(row=0, column=0, sticky='w')
        self.kdj_period_entry = tk.Entry(kdj_frame, width=10)
        self.kdj_period_entry.grid(row=0, column=1)
        self.kdj_period_entry.insert(0, "9")
        
        tk.Label(kdj_frame, text="超卖线:").grid(row=1, column=0, sticky='w')
        self.kdj_oversold_entry = tk.Entry(kdj_frame, width=10)
        self.kdj_oversold_entry.grid(row=1, column=1)
        self.kdj_oversold_entry.insert(0, "20")
        
        tk.Label(kdj_frame, text="超买线:").grid(row=2, column=0, sticky='w')
        self.kdj_overbought_entry = tk.Entry(kdj_frame, width=10)
        self.kdj_overbought_entry.grid(row=2, column=1)
        self.kdj_overbought_entry.insert(0, "80")
        
        # 操作按钮
        button_frame = tk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        self.run_backtest_button = tk.Button(button_frame, text="运行回测", 
                                           command=self.run_backtest, bg='lightgreen')
        self.run_backtest_button.pack(fill=tk.X, pady=2)
        
        self.show_analysis_button = tk.Button(button_frame, text="详细分析",
                                            command=self.show_detailed_analysis, bg='lightblue')
        self.show_analysis_button.pack(fill=tk.X, pady=2)

        self.show_trades_button = tk.Button(button_frame, text="查看买卖点",
                                          command=self.show_trade_points, bg='lightyellow')
        self.show_trades_button.pack(fill=tk.X, pady=2)
        
        # 右侧结果显示区域
        right_frame = tk.Frame(self.backtest_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 结果显示文本框
        self.result_text = scrolledtext.ScrolledText(right_frame, height=15, width=60)
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 图表显示区域
        self.bt_figure = Figure(figsize=(8, 6), dpi=80)
        self.bt_canvas = FigureCanvasTkAgg(self.bt_figure, master=right_frame)
        self.bt_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def query_stock(self):
        """查询股票数据"""
        ts_code = self.code_entry.get()
        start_date = self.start_date_entry.get()
        end_date = self.end_date_entry.get()

        try:
            # 通过市场数据管理器获取股票数据（统一数据接口）
            if self.market_data_manager:
                self.df = self.market_data_manager.get_stock_daily_data(
                    ts_code=ts_code,
                    start_date=start_date,
                    end_date=end_date,
                    use_cache=True
                )
                # 添加到常用股票列表
                self.market_data_manager.add_popular_stock(ts_code)
            else:
                # 备用方案：直接使用API
                self.df = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)

            # 处理日期格式
            if not self.df.empty and 'trade_date' in self.df.columns:
                if self.df['trade_date'].dtype == 'object':
                    self.df['trade_date'] = pd.to_datetime(self.df['trade_date'], format='%Y%m%d')
                self.df = self.df.sort_values('trade_date')

            # 初始化视图控制 - 从最新日期开始显示
            self.view_range = min(100, len(self.df))  # 默认显示100根K线或全部数据
            self.view_start_index = max(0, len(self.df) - self.view_range)  # 从最后开始

            # 预先计算技术指标（避免在绘图时出错）
            self.calculate_indicators()

            # 创建当前视图数据
            end_index = min(len(self.df), self.view_start_index + self.view_range)
            self.current_view_df = self.df.iloc[self.view_start_index:end_index].copy()

            # 更新图表
            self.update_chart()

            # 同步到回测参数
            self.bt_code_entry.delete(0, tk.END)
            self.bt_code_entry.insert(0, ts_code)
            self.bt_start_date_entry.delete(0, tk.END)
            self.bt_start_date_entry.insert(0, start_date)
            self.bt_end_date_entry.delete(0, tk.END)
            self.bt_end_date_entry.insert(0, end_date)

        except Exception as e:
            messagebox.showerror("错误", f"获取数据失败: {str(e)}")

    def toggle_realtime_data(self):
        """切换实时数据获取"""
        if self.realtime_var.get():
            self.start_realtime_data()
        else:
            self.stop_realtime_data()

    def start_realtime_data(self):
        """启动实时数据获取"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            messagebox.showwarning("警告", "请先查询股票数据")
            self.realtime_var.set(False)
            return

        try:
            # 获取更新间隔
            self.realtime_interval = int(self.realtime_interval_entry.get())
            if self.realtime_interval < 5:
                messagebox.showwarning("警告", "更新间隔不能少于5秒")
                self.realtime_interval = 5
                self.realtime_interval_entry.delete(0, tk.END)
                self.realtime_interval_entry.insert(0, "5")
        except ValueError:
            messagebox.showerror("错误", "请输入有效的更新间隔")
            self.realtime_var.set(False)
            return

        # 启动实时数据线程
        self.realtime_running = True
        self.realtime_thread = threading.Thread(target=self.realtime_data_worker, daemon=True)
        self.realtime_thread.start()

        # 更新状态显示
        self.realtime_status_label.config(text="实时数据: 运行中", fg='green')
        print(f"实时数据获取已启动，更新间隔: {self.realtime_interval}秒")

    def force_refresh_realtime_data(self):
        """强制刷新实时数据"""
        if not self.realtime_running:
            messagebox.showinfo("提示", "请先启用实时数据")
            return

        ts_code = self.code_entry.get()
        if not ts_code:
            messagebox.showwarning("警告", "请先输入股票代码")
            return

        print("🔄 强制刷新实时数据...")

        # 清除缓存，强制获取最新数据
        if self.market_data_manager and ts_code in self.market_data_manager.realtime_cache:
            del self.market_data_manager.realtime_cache[ts_code]
            print("✅ 已清除实时数据缓存")

        # 立即获取新数据
        try:
            today = datetime.now().strftime("%Y%m%d")
            realtime_data = self.get_realtime_data(ts_code, today)

            if realtime_data is not None and not realtime_data.empty:
                self.update_realtime_data(realtime_data)
                print("✅ 强制刷新完成")
            else:
                print("⚠️ 未获取到实时数据")
                messagebox.showwarning("警告", "未获取到实时数据，请稍后重试")
        except Exception as e:
            print(f"❌ 强制刷新失败: {str(e)}")
            messagebox.showerror("错误", f"强制刷新失败: {str(e)}")

    def stop_realtime_data(self):
        """停止实时数据获取"""
        self.realtime_running = False
        if self.realtime_thread and self.realtime_thread.is_alive():
            self.realtime_thread.join(timeout=1)

        # 更新状态显示
        self.realtime_status_label.config(text="实时数据: 已停止", fg='red')
        self.last_update_label.config(text="")
        print("实时数据获取已停止")

    def realtime_data_worker(self):
        """实时数据获取工作线程"""
        ts_code = self.code_entry.get()

        while self.realtime_running:
            try:
                # 获取今天的日期
                today = datetime.now().strftime("%Y%m%d")

                # 获取实时数据
                realtime_data = self.get_realtime_data(ts_code, today)

                if realtime_data is not None and not realtime_data.empty:
                    # 在主线程中更新数据和图表
                    self.master.after(0, self.update_realtime_data, realtime_data)

                # 等待指定间隔
                for i in range(self.realtime_interval):
                    if not self.realtime_running:
                        break
                    time.sleep(1)

            except Exception as e:
                print(f"获取实时数据时出错: {str(e)}")
                # 等待一段时间后重试
                time.sleep(10)

    def get_realtime_data(self, ts_code, date):
        """获取指定日期的实时数据（统一接口版本）"""
        print(f"🔄 开始获取 {ts_code} 在 {date} 的实时数据...")

        try:
            # 优先使用市场数据管理器获取最新实时数据（不使用缓存）
            if self.market_data_manager:
                print("🔄 从市场数据管理器获取最新实时数据（不使用缓存）...")
                realtime_data = self.market_data_manager.get_realtime_data(ts_code, use_cache=False)
                if not realtime_data.empty:
                    print(f"✅ 从市场数据管理器获取实时数据成功: 收盘价={realtime_data['close'].iloc[0]:.2f}")
                    return realtime_data
                else:
                    print("⚠️ 市场数据管理器返回空数据，尝试其他方法")

            # 备用方案：原有的多种获取方式
            # 方法1: 尝试获取今日的日线数据
            print("方法1: 尝试获取日线数据...")
            today_data = self.pro.daily(ts_code=ts_code, start_date=date, end_date=date)

            if not today_data.empty:
                print("✅ 成功获取日线数据")
                # 处理日期格式
                today_data['trade_date'] = pd.to_datetime(today_data['trade_date'], format='%Y%m%d')
                # 验证数据有效性
                if self.validate_price_data(today_data):
                    return today_data
                else:
                    print("⚠️ 日线数据验证失败，尝试其他方法")

            # 方法2: 如果没有日线数据，尝试获取实时行情
            print("方法2: 尝试获取实时行情...")
            try:
                stock_code = ts_code.split('.')[0]  # 去掉后缀
                realtime_quote = ts.get_realtime_quotes(stock_code)

                if realtime_quote is not None and not realtime_quote.empty:
                    print("✅ 获取到实时行情数据")

                    # 获取第一行数据
                    quote_row = realtime_quote.iloc[0]

                    # 验证数据有效性
                    price = quote_row.get('price', '0')
                    open_price = quote_row.get('open', '0')
                    high_price = quote_row.get('high', '0')
                    low_price = quote_row.get('low', '0')
                    volume = quote_row.get('volume', '0')

                    # 检查价格是否有效
                    if (price != '0' and price != '' and price != '--' and
                        open_price != '0' and open_price != '' and open_price != '--'):

                        try:
                            # 转换为浮点数
                            price_val = float(price)
                            open_val = float(open_price)
                            high_val = float(high_price) if high_price not in ['0', '', '--'] else price_val
                            low_val = float(low_price) if low_price not in ['0', '', '--'] else price_val
                            vol_val = float(volume) if volume not in ['0', '', '--'] else 1000000

                            # 确保high >= low >= 0
                            if high_val < low_val:
                                high_val, low_val = low_val, high_val
                            if low_val <= 0:
                                low_val = min(price_val, open_val) * 0.99
                            if high_val <= 0:
                                high_val = max(price_val, open_val) * 1.01

                            # 创建数据行
                            realtime_row = {
                                'ts_code': ts_code,
                                'trade_date': pd.to_datetime(date, format='%Y%m%d'),
                                'open': open_val,
                                'high': high_val,
                                'low': low_val,
                                'close': price_val,
                                'vol': vol_val * 100,  # 转换为股
                                'amount': vol_val * price_val * 100
                            }

                            result_df = pd.DataFrame([realtime_row])
                            print(f"✅ 实时行情数据转换成功: 价格={price_val}, 成交量={vol_val}")
                            return result_df

                        except (ValueError, TypeError) as e:
                            print(f"⚠️ 实时行情数据转换失败: {str(e)}")
                    else:
                        print("⚠️ 实时行情数据无效（价格为0或空）")

            except Exception as e:
                print(f"❌ 获取实时行情失败: {str(e)}")

            # 方法3: 尝试获取分钟级数据的最新数据
            print("方法3: 尝试获取分钟数据...")
            try:
                # 获取最近的分钟数据
                minute_data = self.pro.stk_mins(ts_code=ts_code, start_date=date, end_date=date, freq='5min')

                if not minute_data.empty:
                    print(f"✅ 获取到 {len(minute_data)} 条分钟数据")

                    # 聚合分钟数据为日线数据
                    daily_data = {
                        'ts_code': ts_code,
                        'trade_date': pd.to_datetime(date, format='%Y%m%d'),
                        'open': minute_data['open'].iloc[0],
                        'high': minute_data['high'].max(),
                        'low': minute_data['low'].min(),
                        'close': minute_data['close'].iloc[-1],
                        'vol': minute_data['vol'].sum(),
                        'amount': minute_data['amount'].sum()
                    }

                    result_df = pd.DataFrame([daily_data])
                    if self.validate_price_data(result_df):
                        print("✅ 分钟数据聚合成功")
                        return result_df
                    else:
                        print("⚠️ 分钟数据验证失败")

            except Exception as e:
                print(f"❌ 获取分钟数据失败: {str(e)}")

            # 方法4: 如果所有方法都失败，生成模拟数据（基于最后一个有效价格）
            print("方法4: 生成模拟数据...")
            if hasattr(self, 'df') and self.df is not None and not self.df.empty:
                last_price = self.df['close'].iloc[-1]
                if pd.notna(last_price) and last_price > 0:
                    # 生成小幅波动的模拟数据
                    import random
                    change_rate = random.uniform(-0.02, 0.02)  # ±2%的随机变化
                    new_price = last_price * (1 + change_rate)

                    simulated_data = {
                        'ts_code': ts_code,
                        'trade_date': pd.to_datetime(date, format='%Y%m%d'),
                        'open': new_price,
                        'high': new_price * 1.005,
                        'low': new_price * 0.995,
                        'close': new_price,
                        'vol': 1000000,  # 默认成交量
                        'amount': new_price * 1000000
                    }

                    print(f"✅ 生成模拟数据: 价格={new_price:.2f} (基于上一交易日 {last_price:.2f})")
                    return pd.DataFrame([simulated_data])

            print("❌ 所有方法都无法获取有效数据")
            return None

        except Exception as e:
            print(f"❌ 获取实时数据失败: {str(e)}")
            return None

    def validate_price_data(self, data):
        """验证价格数据的有效性"""
        try:
            if data is None or data.empty:
                return False

            row = data.iloc[0]

            # 检查必要字段是否存在且有效
            required_fields = ['open', 'high', 'low', 'close', 'vol']
            for field in required_fields:
                if field not in row or pd.isna(row[field]) or row[field] <= 0:
                    print(f"⚠️ 字段 {field} 无效: {row.get(field, 'N/A')}")
                    return False

            # 检查价格逻辑关系
            if row['high'] < row['low']:
                print(f"⚠️ 最高价 {row['high']} 小于最低价 {row['low']}")
                return False

            if row['close'] > row['high'] or row['close'] < row['low']:
                print(f"⚠️ 收盘价 {row['close']} 超出最高最低价范围 [{row['low']}, {row['high']}]")
                return False

            print(f"✅ 数据验证通过: OHLC=({row['open']:.2f}, {row['high']:.2f}, {row['low']:.2f}, {row['close']:.2f})")
            return True

        except Exception as e:
            print(f"⚠️ 数据验证出错: {str(e)}")
            return False

    def update_realtime_data(self, new_data):
        """更新实时数据到主数据集"""
        try:
            if new_data is None or new_data.empty:
                print("⚠️ 实时数据为空，跳过更新")
                return

            # 验证新数据的有效性
            if not self.validate_price_data(new_data):
                print("⚠️ 实时数据验证失败，跳过更新")
                return

            # 获取新数据的日期
            new_date = new_data['trade_date'].iloc[0]
            print(f"🔄 处理实时数据: {new_date.strftime('%Y-%m-%d')}")

            # 打印新数据内容用于调试
            print(f"新数据内容: 开={new_data['open'].iloc[0]:.2f}, "
                  f"高={new_data['high'].iloc[0]:.2f}, "
                  f"低={new_data['low'].iloc[0]:.2f}, "
                  f"收={new_data['close'].iloc[0]:.2f}")

            # 检查是否已存在该日期的数据
            existing_mask = self.df['trade_date'] == new_date

            if existing_mask.any():
                # 更新现有数据 - 只更新基础价格数据
                basic_columns = ['open', 'high', 'low', 'close', 'vol', 'amount']
                update_values = new_data[basic_columns].iloc[0].values
                self.df.loc[existing_mask, basic_columns] = update_values
                print(f"✅ 更新了 {new_date.strftime('%Y-%m-%d')} 的数据")
            else:
                # 添加新数据 - 只包含基础列，其他列稍后计算
                basic_columns = ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol', 'amount']

                # 创建新行，只包含基础数据
                new_row = {}
                for col in self.df.columns:
                    if col in basic_columns and col in new_data.columns:
                        new_row[col] = new_data[col].iloc[0]
                    else:
                        # 对于技术指标列，先设为NaN，稍后计算
                        new_row[col] = np.nan

                # 添加新行
                new_row_df = pd.DataFrame([new_row])
                self.df = pd.concat([self.df, new_row_df], ignore_index=True)
                self.df = self.df.sort_values('trade_date').reset_index(drop=True)
                print(f"✅ 添加了 {new_date.strftime('%Y-%m-%d')} 的新数据")

            # 验证更新后的数据
            print(f"更新后数据验证: 总记录数={len(self.df)}")
            latest_data = self.df.iloc[-1]
            print(f"最新记录: 日期={latest_data['trade_date'].strftime('%Y-%m-%d')}, "
                  f"收盘价={latest_data['close']:.2f}")

            # 重新计算技术指标
            try:
                self.calculate_indicators()
                print("✅ 技术指标计算完成")
            except Exception as e:
                print(f"⚠️ 技术指标计算失败: {str(e)}")

            # 更新当前视图数据
            try:
                if hasattr(self, 'view_start_index') and hasattr(self, 'view_range'):
                    # 确保视图显示最新数据
                    self.view_start_index = max(0, len(self.df) - self.view_range)
                    end_index = min(len(self.df), self.view_start_index + self.view_range)
                    self.current_view_df = self.df.iloc[self.view_start_index:end_index].copy()
                else:
                    self.current_view_df = self.df.copy()

                print(f"✅ 视图数据更新: 显示{len(self.current_view_df)}条记录")
            except Exception as e:
                print(f"⚠️ 视图数据更新失败: {str(e)}")
                self.current_view_df = self.df.copy()

            # 如果启用了策略显示，重新运行策略以更新买卖点
            if hasattr(self, 'chart_strategy_var') and self.chart_strategy_var.get() != "NONE":
                try:
                    self.update_strategy_signals_realtime()
                    print("✅ 策略信号更新完成")
                except Exception as e:
                    print(f"⚠️ 策略信号更新失败: {str(e)}")

            # 更新图表
            try:
                self.update_chart()
                print("✅ 图表更新完成")
            except Exception as e:
                print(f"⚠️ 图表更新失败: {str(e)}")

            # 检查是否需要执行自动交易
            if hasattr(self, 'auto_trading_active') and self.auto_trading_active:
                try:
                    self.check_and_execute_trading_signal()
                except Exception as e:
                    print(f"⚠️ 自动交易检查失败: {str(e)}")

            # 获取当前价格并比较变化
            current_price = new_data['close'].iloc[0]
            price_change_text = ""
            price_color = 'black'

            if self.last_price is not None:
                price_diff = current_price - self.last_price
                if price_diff > 0:
                    price_change_text = f"↗ +{price_diff:.2f}"
                    price_color = 'red'
                elif price_diff < 0:
                    price_change_text = f"↘ {price_diff:.2f}"
                    price_color = 'green'
                else:
                    price_change_text = "→ 0.00"
                    price_color = 'gray'

            self.last_price = current_price

            # 更新最后更新时间显示
            self.last_update_time = datetime.now()
            self.last_update_label.config(
                text=f"最后更新: {self.last_update_time.strftime('%H:%M:%S')}"
            )

            # 更新价格变化显示
            if price_change_text:
                self.price_change_label.config(text=f"价格: {current_price:.2f} {price_change_text}",
                                             fg=price_color)
            else:
                self.price_change_label.config(text=f"价格: {current_price:.2f}", fg='black')

            print(f"✅ 实时数据更新完成: {new_date.strftime('%Y-%m-%d')}, 价格: {current_price:.2f} {price_change_text}")

        except Exception as e:
            print(f"❌ 更新实时数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def update_strategy_signals_realtime(self):
        """实时更新策略信号"""
        try:
            strategy_type = self.chart_strategy_var.get()
            if strategy_type == "NONE":
                return

            # 创建回测引擎
            engine = BacktestEngine(initial_capital=100000, commission_rate=0.001)

            # 创建策略
            if strategy_type == "MACD":
                strategy = MACDStrategy()
                strategy.set_parameters(fast_period=12, slow_period=26, signal_period=9)
            elif strategy_type == "KDJ":
                strategy = KDJStrategy()
                strategy.set_parameters(k_period=9, oversold=20, overbought=80)
            elif strategy_type == "CUSTOM":
                if hasattr(self, 'custom_strategy_code') and self.custom_strategy_code:
                    strategy = CustomStrategy("自定义策略", self.custom_strategy_code)
                else:
                    return
            else:
                return

            # 运行策略
            engine.set_strategy(strategy)
            engine.load_data(self.df)
            results = engine.run_backtest("REALTIME_STRATEGY")

            # 保存结果
            self.chart_strategy_results = results

            # 获取最新信号
            latest_signal = self.get_latest_signal_from_results(results)
            if latest_signal:
                signal_time = latest_signal.get('date', '')
                signal_type = latest_signal.get('action', '')
                signal_price = latest_signal.get('price', 0)

                print(f"实时策略信号更新: {signal_time} {signal_type} @ {signal_price}")

                # 更新界面显示
                if hasattr(self, 'last_signal_label'):
                    self.last_signal_label.config(
                        text=f"最新信号: {signal_time} {signal_type} @ {signal_price:.2f}"
                    )

        except Exception as e:
            print(f"更新策略信号时出错: {str(e)}")

    def get_latest_signal_from_results(self, results):
        """从策略结果中获取最新信号"""
        try:
            if not results or 'trades' not in results:
                return None

            trades = results['trades']
            if not trades:
                return None

            # 获取最新的交易信号
            latest_trade = trades[-1]
            return {
                'date': latest_trade.get('date', ''),
                'action': latest_trade.get('action', ''),
                'price': latest_trade.get('price', 0),
                'quantity': latest_trade.get('quantity', 0)
            }

        except Exception as e:
            print(f"获取最新信号时出错: {str(e)}")
            return None

    def check_and_execute_trading_signal(self):
        """检查并执行交易信号"""
        try:
            # 获取最新的交易信号
            latest_signal = self.get_latest_trading_signal()

            if latest_signal is None:
                return

            signal_date = latest_signal.get('date', '')
            signal_action = latest_signal.get('action', '')
            signal_price = latest_signal.get('price', 0)

            # 检查信号是否是今天的
            today = datetime.now().strftime('%Y-%m-%d')
            if not signal_date.startswith(today):
                return

            # 检查是否已经执行过这个信号
            signal_key = f"{signal_date}_{signal_action}_{signal_price}"
            if hasattr(self, 'executed_signals') and signal_key in self.executed_signals:
                return

            # 记录已执行的信号
            if not hasattr(self, 'executed_signals'):
                self.executed_signals = set()
            self.executed_signals.add(signal_key)

            # 获取当前持仓状态
            current_holdings = self.get_current_holdings()

            # 根据信号和持仓状态决定是否执行交易
            should_execute = False

            if signal_action == 'BUY' and current_holdings == 0:
                # 买入信号且当前无持仓
                should_execute = True
                action = 'buy'
            elif signal_action == 'SELL' and current_holdings > 0:
                # 卖出信号且当前有持仓
                should_execute = True
                action = 'sell'

            if should_execute:
                # 执行交易
                stock_code = self.trade_code_entry.get()

                if action == 'buy':
                    # 计算买入数量
                    trade_amount = float(self.trade_amount_entry.get())
                    quantity = int(trade_amount / signal_price / 100) * 100  # 按手计算

                    if quantity >= 100:  # 至少一手
                        self.execute_trade_action('buy', stock_code, quantity, signal_price)
                        self.log_trading_message(f"🤖 自动买入: {stock_code} {quantity}股 @ {signal_price:.2f}")

                elif action == 'sell':
                    # 卖出全部持仓
                    if current_holdings >= 100:
                        self.execute_trade_action('sell', stock_code, current_holdings, signal_price)
                        self.log_trading_message(f"🤖 自动卖出: {stock_code} {current_holdings}股 @ {signal_price:.2f}")

        except Exception as e:
            print(f"检查和执行交易信号时出错: {str(e)}")
            if hasattr(self, 'log_trading_message'):
                self.log_trading_message(f"自动交易执行失败: {str(e)}")

    def get_current_holdings(self):
        """获取当前持仓数量（简化版本，用于实时交易决策）"""
        try:
            stock_code = self.trade_code_entry.get() if hasattr(self, 'trade_code_entry') else self.code_entry.get()

            # 如果有网页交易的持仓信息，使用它
            if hasattr(self, 'current_holdings') and stock_code in self.current_holdings:
                return self.current_holdings[stock_code].get('quantity', 0)

            # 否则返回0（假设空仓）
            return 0

        except Exception as e:
            print(f"获取持仓信息时出错: {str(e)}")
            return 0

    def execute_trade_action(self, action, stock_code, quantity, price):
        """执行交易操作（实时交易专用）"""
        try:
            if not hasattr(self, 'is_connected') or not self.is_connected:
                print("未连接到交易网站，无法执行交易")
                return False

            # 调用现有的交易执行函数
            if action.upper() == 'BUY':
                self.execute_trade("BUY", stock_code, quantity, price)
            elif action.upper() == 'SELL':
                self.execute_trade("SELL", stock_code, quantity, price)
            else:
                print(f"未知的交易操作: {action}")
                return False

            return True

        except Exception as e:
            print(f"执行交易操作失败: {str(e)}")
            return False

    def on_closing(self):
        """窗口关闭时的清理工作"""
        print("开始清理股票软件资源...")

        # 停止实时数据获取
        if hasattr(self, 'realtime_running') and self.realtime_running:
            self.stop_realtime_data()

        # 停止网页交易相关线程
        if hasattr(self, 'auto_trading_running') and self.auto_trading_running:
            self.stop_auto_trading()

        # 停止多股票监控
        if hasattr(self, 'multi_monitor_running') and self.multi_monitor_running:
            try:
                self.stop_multi_monitor()
            except:
                pass

        # 停止多股票自动交易
        if hasattr(self, 'multi_auto_trading_enabled') and self.multi_auto_trading_enabled:
            try:
                self.stop_multi_auto_trading()
            except:
                pass

        # 停止市场数据更新和预加载
        if hasattr(self, 'market_data_manager') and self.market_data_manager:
            try:
                self.market_data_manager.stop_preload()
                self.market_data_manager.stop_update()
            except:
                pass

        # 关闭浏览器
        if hasattr(self, 'driver') and self.driver:
            try:
                self.driver.quit()
            except:
                pass

        # 关闭网页交易浏览器
        if hasattr(self, 'web_driver') and self.web_driver:
            try:
                self.web_driver.quit()
            except:
                pass

        # 清理登录程序进程
        self.cleanup_login_process()

        print("股票软件资源清理完成")

        # 销毁窗口
        self.master.destroy()

        # 强制退出所有相关进程
        self.force_exit_all_processes()

    def cleanup_login_process(self):
        """清理登录程序进程"""
        try:
            import os
            import signal

            # 查找登录进程标识文件
            pid_file = os.path.join(os.path.dirname(__file__), "login_process.pid")

            if os.path.exists(pid_file):
                try:
                    with open(pid_file, 'r', encoding='utf-8') as f:
                        login_pid = int(f.read().strip())

                    print(f"找到登录进程PID: {login_pid}")

                    # 尝试终止登录进程
                    try:
                        if os.name == 'nt':  # Windows
                            import subprocess
                            subprocess.run(['taskkill', '/F', '/PID', str(login_pid)],
                                         capture_output=True, check=False)
                            print(f"已终止登录进程 {login_pid}")
                        else:  # Unix/Linux
                            os.kill(login_pid, signal.SIGTERM)
                            print(f"已发送终止信号给登录进程 {login_pid}")
                    except ProcessLookupError:
                        print("登录进程已不存在")
                    except Exception as e:
                        print(f"终止登录进程失败: {e}")

                    # 删除PID文件
                    os.remove(pid_file)
                    print("登录进程标识文件已删除")

                except Exception as e:
                    print(f"处理登录进程标识文件失败: {e}")
            else:
                print("未找到登录进程标识文件")

        except Exception as e:
            print(f"清理登录进程失败: {e}")

    def force_exit_all_processes(self):
        """强制退出所有相关进程"""
        try:
            import os
            import sys
            import threading
            import time

            def delayed_exit():
                """延迟强制退出"""
                time.sleep(1)  # 等待1秒让清理工作完成
                try:
                    print("强制退出所有进程...")
                    os._exit(0)  # 强制退出
                except:
                    pass

            # 在后台线程中执行延迟退出
            exit_thread = threading.Thread(target=delayed_exit, daemon=True)
            exit_thread.start()

        except Exception as e:
            print(f"强制退出失败: {e}")
            # 最后的保险措施
            try:
                import os
                os._exit(0)
            except:
                pass

    def toggle_crosshair(self):
        """切换十字光标显示"""
        self.show_crosshair = self.crosshair_var.get()
        if not self.show_crosshair:
            self.clear_crosshair()

    def calculate_indicators(self):
        """预先计算所有技术指标"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return

        try:
            # 计算MACD指标
            ewam12 = self.df['close'].ewm(span=12, adjust=False).mean()
            ewam26 = self.df['close'].ewm(span=26, adjust=False).mean()
            self.df['dif'] = ewam12 - ewam26
            self.df['dea'] = self.df['dif'].ewm(span=9, adjust=False).mean()
            self.df['macd'] = 2 * (self.df['dif'] - self.df['dea'])

            # 计算KDJ指标
            low_list = self.df['low'].rolling(9, min_periods=9).min()
            low_list.fillna(value=self.df['low'].expanding().min(), inplace=True)
            high_list = self.df['high'].rolling(9, min_periods=9).max()
            high_list.fillna(value=self.df['high'].expanding().max(), inplace=True)
            rsv = (self.df['close'] - low_list) / (high_list - low_list) * 100
            self.df['k'] = pd.DataFrame(rsv.ewm(com=2).mean())
            self.df['d'] = self.df['k'].ewm(com=2).mean()
            self.df['j'] = 3 * self.df['k'] - 2 * self.df['d']

        except Exception as e:
            print(f"计算技术指标时出错: {str(e)}")

    def update_chart(self):
        """更新图表"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return

        # 如果没有当前视图数据，使用全部数据
        if not hasattr(self, 'current_view_df'):
            self.current_view_df = self.df.copy()

        self.figure.clear()
        indicator = self.indicator_var.get()

        if indicator == "MACD":
            self.plot_macd()
        elif indicator == "KDJ":
            self.plot_kdj()
        elif indicator == "CHANLUN":
            self.plot_chanlun()

        self.canvas.draw()

    def update_chart_with_signals(self):
        """更新图表并显示买卖点"""
        self.update_chart()

    def reset_chart_view(self):
        """重置图表视图"""
        if hasattr(self, 'df') and self.df is not None and not self.df.empty:
            # 重置视图控制变量 - 从最新日期开始
            self.view_range = min(100, len(self.df))  # 默认显示100根K线或全部数据
            self.view_start_index = max(0, len(self.df) - self.view_range)  # 从最后开始

            # 创建当前视图数据
            end_index = min(len(self.df), self.view_start_index + self.view_range)
            self.current_view_df = self.df.iloc[self.view_start_index:end_index].copy()

            # 更新图表
            self.update_chart()
        elif hasattr(self, 'figure'):
            for ax in self.figure.get_axes():
                ax.relim()
                ax.autoscale()
            self.canvas.draw()

    def update_chart_strategy(self):
        """更新图表策略显示"""
        strategy_type = self.chart_strategy_var.get()

        if strategy_type == "NONE":
            # 清除策略信号，只显示基本K线图和指标
            self.chart_strategy_results = None
            self.show_signals_var.set(False)
        else:
            # 如果有数据，自动运行策略
            if hasattr(self, 'df') and self.df is not None and not self.df.empty:
                self.run_chart_strategy()

        self.update_chart()

    def run_chart_strategy(self):
        """在图表中运行选定的策略"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            messagebox.showwarning("警告", "请先获取股票数据")
            return

        strategy_type = self.chart_strategy_var.get()
        if strategy_type == "NONE":
            return

        try:
            # 创建回测引擎
            engine = BacktestEngine(initial_capital=100000, commission_rate=0.001)

            # 创建策略
            if strategy_type == "MACD":
                strategy = MACDStrategy()
                strategy.set_parameters(fast_period=12, slow_period=26, signal_period=9)
            elif strategy_type == "KDJ":
                strategy = KDJStrategy()
                strategy.set_parameters(k_period=9, oversold=20, overbought=80)
            elif strategy_type == "CUSTOM":
                if hasattr(self, 'custom_strategy_code') and self.custom_strategy_code:
                    strategy = CustomStrategy("自定义策略", self.custom_strategy_code)
                else:
                    messagebox.showwarning("警告", "请先在自定义策略页面编写策略代码")
                    return
            else:
                return

            # 运行策略
            engine.set_strategy(strategy)
            engine.load_data(self.df)
            results = engine.run_backtest("CHART_STRATEGY")

            # 保存结果
            self.chart_strategy_results = results

            # 自动显示买卖点
            self.show_signals_var.set(True)

            # 更新图表
            self.update_chart()

            # 显示简要结果
            total_return = results.get('total_return', 0)
            total_trades = results.get('total_trades', 0)
            win_rate = results.get('win_rate', 0)

            messagebox.showinfo("策略运行完成",
                              f"策略: {strategy.name}\n"
                              f"总收益率: {total_return:.2%}\n"
                              f"交易次数: {total_trades}\n"
                              f"胜率: {win_rate:.2%}")

        except Exception as e:
            messagebox.showerror("错误", f"运行策略失败: {str(e)}")

    def init_view_control(self):
        """初始化视图控制变量"""
        self.view_start_index = 0  # 当前显示的起始索引
        self.view_range = 100      # 当前显示的K线数量
        self.zoom_factor = 1.0     # 缩放因子
        self.min_view_range = 10   # 最小显示范围
        self.max_view_range = 500  # 最大显示范围
        self.pan_step = 5          # 平移步长

    def setup_keyboard_navigation(self):
        """设置键盘导航"""
        # 让canvas获得焦点以接收键盘事件
        self.canvas.get_tk_widget().focus_set()
        self.canvas.get_tk_widget().bind('<Key>', self.on_key_press)
        self.canvas.get_tk_widget().bind('<Button-1>', lambda e: self.canvas.get_tk_widget().focus_set())

        # 绑定鼠标滚轮事件
        self.canvas.get_tk_widget().bind('<MouseWheel>', self.on_mouse_wheel)

        # 绑定鼠标移动事件（十字光标）
        self.canvas.get_tk_widget().bind('<Motion>', self.on_mouse_move)
        self.canvas.get_tk_widget().bind('<Enter>', self.on_mouse_enter)
        self.canvas.get_tk_widget().bind('<Leave>', self.on_mouse_leave)

        # 初始化十字光标变量
        self.crosshair_lines = None
        self.info_text = None
        self.show_crosshair = True
        self.last_crosshair_pos = None  # 记录上次位置，避免重复绘制

    def on_key_press(self, event):
        """处理键盘按键事件"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return

        key = event.keysym
        data_length = len(self.df)

        # 记录是否需要更新视图
        need_update = False

        # 左右箭头键：平移视图
        if key == 'Left':
            new_start = max(0, self.view_start_index - self.pan_step)
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True
        elif key == 'Right':
            max_start = max(0, data_length - self.view_range)
            new_start = min(max_start, self.view_start_index + self.pan_step)
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True

        # 上下箭头键：缩放视图
        elif key == 'Up':
            # 放大（显示更少K线，更详细）
            new_range = max(self.min_view_range, int(self.view_range * 0.8))
            if new_range != self.view_range:
                # 调整起始位置以保持中心点
                center_index = self.view_start_index + self.view_range // 2
                self.view_range = new_range
                self.view_start_index = max(0, center_index - self.view_range // 2)
                need_update = True

        elif key == 'Down':
            # 缩小（显示更多K线，更全面）
            new_range = min(self.max_view_range, int(self.view_range * 1.25))
            if new_range != self.view_range:
                # 调整起始位置以保持中心点
                center_index = self.view_start_index + self.view_range // 2
                self.view_range = new_range
                max_start = max(0, data_length - self.view_range)
                self.view_start_index = min(max_start, max(0, center_index - self.view_range // 2))
                need_update = True

        # Home/End键：快速定位
        elif key == 'Home':
            new_start = 0
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True
        elif key == 'End':
            new_start = max(0, data_length - self.view_range)
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True

        # Page Up/Down：大幅平移
        elif key == 'Prior':  # Page Up
            new_start = max(0, self.view_start_index - self.view_range // 2)
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True
        elif key == 'Next':   # Page Down
            max_start = max(0, data_length - self.view_range)
            new_start = min(max_start, self.view_start_index + self.view_range // 2)
            if new_start != self.view_start_index:
                self.view_start_index = new_start
                need_update = True

        # 只有在确实需要时才更新视图
        if need_update:
            self.update_chart_view()

    def on_mouse_wheel(self, event):
        """处理鼠标滚轮事件"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return

        # 获取鼠标位置相对于图表的比例
        widget_width = self.canvas.get_tk_widget().winfo_width()
        mouse_x = event.x
        mouse_ratio = mouse_x / widget_width if widget_width > 0 else 0.5

        # 计算当前鼠标位置对应的数据索引
        current_mouse_index = self.view_start_index + int(self.view_range * mouse_ratio)

        # 根据滚轮方向调整缩放（兼容不同系统）
        # Windows: delta > 0 向上滚动，delta < 0 向下滚动
        # Linux/Mac: delta 可能是 1 或 -1
        zoom_in = False
        if hasattr(event, 'delta'):
            zoom_in = event.delta > 0
        elif hasattr(event, 'num'):
            zoom_in = event.num == 4  # Linux 鼠标滚轮向上

        if zoom_in:  # 向上滚动，放大（显示更少K线）
            new_range = max(self.min_view_range, int(self.view_range * 0.8))
        else:  # 向下滚动，缩小（显示更多K线）
            new_range = min(self.max_view_range, int(self.view_range * 1.25))

        if new_range != self.view_range:
            # 以鼠标位置为中心进行缩放
            self.view_range = new_range
            new_mouse_ratio = mouse_ratio  # 保持鼠标位置不变
            self.view_start_index = max(0, current_mouse_index - int(self.view_range * new_mouse_ratio))

            # 确保不超出数据范围
            data_length = len(self.df)
            max_start = max(0, data_length - self.view_range)
            self.view_start_index = min(max_start, self.view_start_index)

            self.update_chart_view()

    def on_mouse_move(self, event):
        """处理鼠标移动事件（十字光标）"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return
        if not hasattr(self, 'current_view_df') or self.current_view_df is None:
            return
        if not self.show_crosshair:
            return

        # 检查鼠标位置是否有显著变化（减少重绘频率）
        current_pos = (event.x, event.y)
        if self.last_crosshair_pos:
            dx = abs(current_pos[0] - self.last_crosshair_pos[0])
            dy = abs(current_pos[1] - self.last_crosshair_pos[1])
            if dx < 3 and dy < 3:  # 移动距离小于3像素时不更新
                return

        self.last_crosshair_pos = current_pos

        # 获取鼠标在canvas中的位置
        try:
            # 获取图表的axes
            axes = self.figure.get_axes()
            if not axes:
                return

            # 使用第一个axes（K线图）
            ax = axes[0]

            # 将canvas坐标转换为数据坐标
            inv = ax.transData.inverted()
            x_data, y_data = inv.transform((event.x, event.y))

            # 更新十字光标
            self.update_crosshair(ax, x_data, y_data)

        except Exception as e:
            pass  # 忽略转换错误

    def on_mouse_enter(self, event):
        """鼠标进入图表区域"""
        self.show_crosshair = True

    def on_mouse_leave(self, event):
        """鼠标离开图表区域"""
        self.show_crosshair = False
        self.clear_crosshair()

    def update_crosshair(self, ax, x_data, y_data):
        """更新十字光标"""
        try:
            # 清除之前的十字光标
            self.clear_crosshair()

            # 获取axes的范围
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()

            # 检查鼠标是否在有效范围内
            if x_data < xlim[0] or x_data > xlim[1] or y_data < ylim[0] or y_data > ylim[1]:
                return

            # 绘制十字光标线
            self.crosshair_lines = []

            # 垂直线
            vline = ax.axvline(x=x_data, color='gray', linestyle='--', alpha=0.7, linewidth=1)
            self.crosshair_lines.append(vline)

            # 水平线
            hline = ax.axhline(y=y_data, color='gray', linestyle='--', alpha=0.7, linewidth=1)
            self.crosshair_lines.append(hline)

            # 查找最接近的K线数据
            closest_data = self.find_closest_kline_data(x_data)
            if closest_data:
                # 显示信息文本
                self.show_crosshair_info(ax, x_data, y_data, closest_data)

            # 重绘canvas
            self.canvas.draw_idle()

        except Exception as e:
            pass  # 忽略绘制错误

    def clear_crosshair(self):
        """清除十字光标"""
        try:
            need_redraw = False

            if self.crosshair_lines:
                for line in self.crosshair_lines:
                    line.remove()
                self.crosshair_lines = None
                need_redraw = True

            if self.info_text:
                self.info_text.remove()
                self.info_text = None
                need_redraw = True

            # 重置位置记录
            self.last_crosshair_pos = None

            # 只有在确实需要时才重绘
            if need_redraw:
                self.canvas.draw_idle()
        except:
            pass

    def find_closest_kline_data(self, x_data):
        """查找最接近鼠标位置的K线数据"""
        try:
            import matplotlib.dates as mdates

            # 将x_data转换为日期
            target_date = mdates.num2date(x_data)

            # 在当前视图数据中查找最接近的日期
            dates = self.current_view_df['trade_date']
            date_nums = mdates.date2num(dates)

            # 找到最接近的索引
            closest_idx = None
            min_distance = float('inf')

            for i, date_num in enumerate(date_nums):
                distance = abs(date_num - x_data)
                if distance < min_distance:
                    min_distance = distance
                    closest_idx = i

            if closest_idx is not None:
                row = self.current_view_df.iloc[closest_idx]
                return {
                    'date': row['trade_date'],
                    'open': row['open'],
                    'high': row['high'],
                    'low': row['low'],
                    'close': row['close'],
                    'volume': row.get('volume', 0)
                }

        except Exception as e:
            pass

        return None

    def show_crosshair_info(self, ax, x_data, y_data, kline_data):
        """显示十字光标信息"""
        try:
            # 格式化信息文本
            date_str = kline_data['date'].strftime('%Y-%m-%d')
            info_lines = [
                f"日期: {date_str}",
                f"开盘: {kline_data['open']:.2f}",
                f"最高: {kline_data['high']:.2f}",
                f"最低: {kline_data['low']:.2f}",
                f"收盘: {kline_data['close']:.2f}",
                f"成交量: {kline_data['volume']:,}" if kline_data['volume'] > 0 else ""
            ]

            # 过滤空行
            info_lines = [line for line in info_lines if line]
            info_text = '\n'.join(info_lines)

            # 确定文本位置（避免超出边界）
            xlim = ax.get_xlim()
            ylim = ax.get_ylim()

            # 文本框位置策略：优先显示在右上角，如果空间不够则调整
            text_x = x_data + (xlim[1] - xlim[0]) * 0.02
            text_y = ylim[1] - (ylim[1] - ylim[0]) * 0.05

            # 如果右侧空间不够，显示在左侧
            if text_x > xlim[1] - (xlim[1] - xlim[0]) * 0.25:
                text_x = x_data - (xlim[1] - xlim[0]) * 0.02
                ha = 'right'
            else:
                ha = 'left'

            # 显示信息文本
            self.info_text = ax.text(text_x, text_y, info_text,
                                   bbox=dict(boxstyle='round,pad=0.5',
                                           facecolor='lightyellow',
                                           alpha=0.9,
                                           edgecolor='gray'),
                                   fontsize=9, ha=ha, va='top',
                                   family='monospace')

        except Exception as e:
            pass

    def update_chart_view(self):
        """更新图表视图（应用当前的视图范围）"""
        if not hasattr(self, 'df') or self.df is None or self.df.empty:
            return

        # 计算实际的结束索引
        end_index = min(len(self.df), self.view_start_index + self.view_range)

        # 创建当前视图的数据切片
        self.current_view_df = self.df.iloc[self.view_start_index:end_index].copy()

        # 确保技术指标数据同步（如果已经计算过）
        self.sync_indicators_to_view()

        # 更新图表
        self.update_chart()

    def sync_indicators_to_view(self):
        """同步技术指标数据到当前视图"""
        if not hasattr(self, 'current_view_df') or self.current_view_df is None:
            return

        # 只有在指标已经计算过的情况下才同步
        start_idx = self.view_start_index if hasattr(self, 'view_start_index') else 0
        end_idx = start_idx + len(self.current_view_df)

        # 同步MACD指标（只有在已计算的情况下）
        if hasattr(self.df, 'columns') and 'dif' in self.df.columns:
            try:
                self.current_view_df['dif'] = self.df['dif'].iloc[start_idx:end_idx].values
                self.current_view_df['dea'] = self.df['dea'].iloc[start_idx:end_idx].values
                self.current_view_df['macd'] = self.df['macd'].iloc[start_idx:end_idx].values
            except (KeyError, IndexError):
                pass  # 如果出错就跳过，让绘图方法重新计算

        # 同步KDJ指标（只有在已计算的情况下）
        if hasattr(self.df, 'columns') and 'k' in self.df.columns:
            try:
                self.current_view_df['k'] = self.df['k'].iloc[start_idx:end_idx].values
                self.current_view_df['d'] = self.df['d'].iloc[start_idx:end_idx].values
                self.current_view_df['j'] = self.df['j'].iloc[start_idx:end_idx].values
            except (KeyError, IndexError):
                pass  # 如果出错就跳过，让绘图方法重新计算

    def plot_macd(self):
        """绘制MACD图表"""
        # 确保MACD指标已计算
        if not hasattr(self.df, 'dif') or 'dif' not in self.df.columns:
            self.calculate_indicators()

        # 创建子图，共享x轴以实现同步缩放
        ax1 = self.figure.add_subplot(211)
        ax2 = self.figure.add_subplot(212, sharex=ax1)

        # 使用当前视图数据绘制K线
        dates = self.current_view_df['trade_date']
        self.plot_candlestick(ax1, dates)

        # 绘制买卖点（如果有回测结果且用户选择显示）
        if (hasattr(self, 'show_signals_var') and self.show_signals_var.get()):
            # 优先使用图表策略结果，其次使用回测结果
            if hasattr(self, 'chart_strategy_results') and self.chart_strategy_results is not None:
                self.plot_trade_signals_on_chart(ax1, self.chart_strategy_results)
            elif hasattr(self, 'backtest_results') and self.backtest_results is not None:
                self.plot_trade_signals_on_chart(ax1, self.backtest_results)

        # 绘制MACD（使用当前视图数据）
        try:
            view_dif = self.current_view_df['dif']
            view_dea = self.current_view_df['dea']
            view_macd = self.current_view_df['macd']
        except KeyError:
            # 如果当前视图数据中没有MACD指标，从完整数据中获取
            start_idx = self.view_start_index if hasattr(self, 'view_start_index') else 0
            end_idx = start_idx + len(self.current_view_df)
            view_dif = self.df['dif'].iloc[start_idx:end_idx]
            view_dea = self.df['dea'].iloc[start_idx:end_idx]
            view_macd = self.df['macd'].iloc[start_idx:end_idx]

        ax2.plot(dates, view_dif, color='blue', label='DIF')
        ax2.plot(dates, view_dea, color='orange', label='DEA')

        # 绘制MACD柱状图
        colors = ['green' if x >= 0 else 'red' for x in view_macd]
        ax2.bar(dates, view_macd, color=colors, width=0.5)

        # 添加视图信息到标题
        total_days = len(self.df)
        current_range = len(self.current_view_df)
        start_idx = self.view_start_index if hasattr(self, 'view_start_index') else 0

        ax1.set_title(f'K线图 ({start_idx+1}-{start_idx+current_range}/{total_days})' +
                     (' (含买卖点)' if (hasattr(self, 'show_signals_var') and self.show_signals_var.get()) else ''))
        ax2.set_title('MACD')
        ax1.grid(True, alpha=0.3)
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 隐藏上图的x轴标签，避免重复
        ax1.tick_params(axis='x', labelbottom=False)

        self.figure.tight_layout()

    def plot_kdj(self):
        """绘制KDJ图表"""
        # 确保KDJ指标已计算
        if not hasattr(self.df, 'k') or 'k' not in self.df.columns:
            self.calculate_indicators()

        # 创建子图，共享x轴以实现同步缩放
        ax1 = self.figure.add_subplot(211)
        ax2 = self.figure.add_subplot(212, sharex=ax1)

        # 使用当前视图数据绘制K线
        dates = self.current_view_df['trade_date']
        self.plot_candlestick(ax1, dates)

        # 绘制买卖点（如果有回测结果且用户选择显示）
        if (hasattr(self, 'show_signals_var') and self.show_signals_var.get()):
            # 优先使用图表策略结果，其次使用回测结果
            if hasattr(self, 'chart_strategy_results') and self.chart_strategy_results is not None:
                self.plot_trade_signals_on_chart(ax1, self.chart_strategy_results)
            elif hasattr(self, 'backtest_results') and self.backtest_results is not None:
                self.plot_trade_signals_on_chart(ax1, self.backtest_results)

        # 绘制KDJ（使用当前视图数据）
        try:
            view_k = self.current_view_df['k']
            view_d = self.current_view_df['d']
            view_j = self.current_view_df['j']
        except KeyError:
            # 如果当前视图数据中没有KDJ指标，从完整数据中获取
            start_idx = self.view_start_index if hasattr(self, 'view_start_index') else 0
            end_idx = start_idx + len(self.current_view_df)
            view_k = self.df['k'].iloc[start_idx:end_idx]
            view_d = self.df['d'].iloc[start_idx:end_idx]
            view_j = self.df['j'].iloc[start_idx:end_idx]

        ax2.plot(dates, view_k, color='red', label='K')
        ax2.plot(dates, view_d, color='green', label='D')
        ax2.plot(dates, view_j, color='blue', label='J')

        # 添加视图信息到标题
        total_days = len(self.df)
        current_range = len(self.current_view_df)
        start_idx = self.view_start_index if hasattr(self, 'view_start_index') else 0

        ax1.set_title(f'K线图 ({start_idx+1}-{start_idx+current_range}/{total_days})' +
                     (' (含买卖点)' if (hasattr(self, 'show_signals_var') and self.show_signals_var.get()) else ''))
        ax2.set_title('KDJ')
        ax1.grid(True, alpha=0.3)
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='best')

        # 隐藏上图的x轴标签，避免重复
        ax1.tick_params(axis='x', labelbottom=False)

        self.figure.tight_layout()

    def plot_candlestick(self, ax, dates):
        """绘制K线图"""
        from matplotlib.patches import Rectangle
        import matplotlib.dates as mdates

        # 计算K线柱的宽度，根据数据量自动调整
        total_days = len(dates)

        # 根据数据量动态调整K线宽度
        if total_days <= 20:
            # 很少数据时，K线柱很宽，便于观察细节
            candle_width = 0.8
            line_width = 8
            shadow_width = 2
        elif total_days <= 50:
            # 少量数据时，K线柱较宽
            candle_width = 0.6
            line_width = 6
            shadow_width = 1.5
        elif total_days <= 100:
            # 中等数据量
            candle_width = 0.4
            line_width = 4
            shadow_width = 1
        elif total_days <= 200:
            # 较多数据量
            candle_width = 0.3
            line_width = 3
            shadow_width = 0.8
        else:
            # 大量数据时，K线柱较细但仍可见
            candle_width = 0.2
            line_width = 2
            shadow_width = 0.5

        # 转换日期为数值以便绘制
        date_nums = mdates.date2num(dates)

        # 绘制K线（使用当前视图数据）
        for idx in range(len(dates)):
            date_num = date_nums[idx]
            open_price = self.current_view_df.iloc[idx]['open']
            high_price = self.current_view_df.iloc[idx]['high']
            low_price = self.current_view_df.iloc[idx]['low']
            close_price = self.current_view_df.iloc[idx]['close']

            # 检查数据有效性，跳过NaN值
            if (pd.isna(open_price) or pd.isna(high_price) or
                pd.isna(low_price) or pd.isna(close_price)):
                print(f"⚠️ 跳过无效K线数据: 索引{idx}, 日期{dates.iloc[idx].strftime('%Y-%m-%d')}")
                continue

            # 确保价格数据合理
            if (open_price <= 0 or high_price <= 0 or
                low_price <= 0 or close_price <= 0):
                print(f"⚠️ 跳过无效价格数据: 索引{idx}, 开={open_price}, 高={high_price}, 低={low_price}, 收={close_price}")
                continue

            # 绘制上下影线
            ax.plot([date_num, date_num], [low_price, high_price],
                   color='black', linewidth=shadow_width, solid_capstyle='round')

            # 确定K线颜色
            if close_price >= open_price:
                # 阳线 - 红色
                color = 'red'
                edge_color = 'darkred'
            else:
                # 阴线 - 绿色
                color = 'green'
                edge_color = 'darkgreen'

            # 绘制实体部分（使用矩形）
            body_height = abs(close_price - open_price)
            body_bottom = min(open_price, close_price)

            if body_height > 0:  # 有实体的K线
                rect = Rectangle((date_num - candle_width/2, body_bottom),
                               candle_width, body_height,
                               facecolor=color, edgecolor=edge_color,
                               linewidth=0.5, alpha=0.8)
                ax.add_patch(rect)
            else:  # 十字星（开盘价=收盘价）
                ax.plot([date_num - candle_width/2, date_num + candle_width/2],
                       [open_price, open_price],
                       color=edge_color, linewidth=line_width/2)

        # 设置x轴为日期格式
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))

        # 自动调整x轴显示范围
        ax.set_xlim(date_nums[0] - 0.5, date_nums[-1] + 0.5)

        # 根据数据量调整x轴标签密度
        if total_days <= 30:
            # 少量数据时显示更多标签
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, total_days//15)))
            ax.tick_params(axis='x', rotation=45, labelsize=8)
        elif total_days <= 100:
            # 中等数据量时适中显示
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, total_days//10)))
            ax.tick_params(axis='x', rotation=45, labelsize=7)
        else:
            # 大量数据时减少标签密度
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=max(1, total_days//8)))
            ax.tick_params(axis='x', rotation=45, labelsize=6)

        # 自动调整y轴范围，留出适当边距（使用当前视图数据，排除NaN值）
        try:
            # 过滤掉NaN值
            valid_data = self.current_view_df[['low', 'high']].dropna()
            if not valid_data.empty:
                price_min = valid_data['low'].min()
                price_max = valid_data['high'].max()

                if pd.notna(price_min) and pd.notna(price_max) and price_min > 0 and price_max > 0:
                    price_range = price_max - price_min
                    margin = max(price_range * 0.05, 0.01)  # 至少0.01的边距
                    ax.set_ylim(price_min - margin, price_max + margin)
                    print(f"✅ Y轴范围设置: {price_min-margin:.2f} - {price_max+margin:.2f}")
                else:
                    print("⚠️ 价格数据无效，使用默认Y轴范围")
                    ax.set_ylim(0, 100)
            else:
                print("⚠️ 没有有效的价格数据，使用默认Y轴范围")
                ax.set_ylim(0, 100)
        except Exception as e:
            print(f"⚠️ Y轴范围计算失败: {str(e)}，使用默认范围")
            ax.set_ylim(0, 100)



    def plot_trade_signals_on_chart(self, ax, results=None):
        """在K线图上绘制买卖点"""
        # 使用传入的results参数，如果没有则使用默认的backtest_results
        if results is None:
            if not hasattr(self, 'backtest_results') or self.backtest_results is None:
                return
            results = self.backtest_results

        # 获取交易记录
        trades_df = results.get('trades')
        if trades_df is None or trades_df.empty:
            return

        # 获取信号数据
        signal_data = results.get('signal_data')
        if signal_data is None or signal_data.empty:
            return

        import matplotlib.dates as mdates

        # 将交易日期转换为datetime格式以便匹配
        try:
            # 处理买入点
            buy_trades = trades_df[trades_df['action'] == 'BUY']
            for _, trade in buy_trades.iterrows():
                trade_date = pd.to_datetime(trade['date'])
                trade_price = trade['price']

                # 转换为matplotlib日期格式
                trade_date_num = mdates.date2num(trade_date)

                # 在K线图上标记买入点
                ax.scatter(trade_date_num, trade_price, color='red', marker='^',
                          s=200, zorder=10, label='买入' if _ == buy_trades.index[0] else "",
                          edgecolors='darkred', linewidth=1)

                # 添加价格标注
                ax.annotate(f'买入\n¥{trade_price:.2f}',
                           xy=(trade_date_num, trade_price),
                           xytext=(15, 25), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.4', facecolor='red', alpha=0.8),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.1',
                                         color='darkred', lw=1.5),
                           fontsize=9, color='white', weight='bold')

            # 处理卖出点
            sell_trades = trades_df[trades_df['action'] == 'SELL']
            for _, trade in sell_trades.iterrows():
                trade_date = pd.to_datetime(trade['date'])
                trade_price = trade['price']

                # 转换为matplotlib日期格式
                trade_date_num = mdates.date2num(trade_date)

                # 在K线图上标记卖出点
                ax.scatter(trade_date_num, trade_price, color='green', marker='v',
                          s=200, zorder=10, label='卖出' if _ == sell_trades.index[0] else "",
                          edgecolors='darkgreen', linewidth=1)

                # 添加价格标注
                ax.annotate(f'卖出\n¥{trade_price:.2f}',
                           xy=(trade_date_num, trade_price),
                           xytext=(15, -35), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.4', facecolor='green', alpha=0.8),
                           arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=-0.1',
                                         color='darkgreen', lw=1.5),
                           fontsize=9, color='white', weight='bold')

            # 添加图例
            if not buy_trades.empty or not sell_trades.empty:
                ax.legend(loc='upper left', framealpha=0.9)

        except Exception as e:
            print(f"绘制买卖点时出错: {str(e)}")

    def run_backtest(self):
        """运行回测"""
        def backtest_thread():
            try:
                # 获取参数
                ts_code = self.bt_code_entry.get()
                start_date = self.bt_start_date_entry.get()
                end_date = self.bt_end_date_entry.get()
                initial_capital = float(self.initial_capital_entry.get())
                commission_rate = float(self.commission_entry.get())
                strategy_type = self.strategy_var.get()

                # 更新状态
                self.master.after(0, lambda: self.result_text.insert(tk.END, "正在获取数据...\n"))

                # 通过市场数据管理器获取数据（统一接口）
                if self.market_data_manager:
                    data = self.market_data_manager.get_stock_daily_data(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date,
                        use_cache=True
                    )
                else:
                    # 备用方案
                    data = self.pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
                    data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d')
                    data = data.sort_values('trade_date')

                # 创建回测引擎
                engine = BacktestEngine(initial_capital=initial_capital, commission_rate=commission_rate)

                # 创建策略
                if strategy_type == "MACD":
                    strategy = MACDStrategy()
                    fast_period = int(self.macd_fast_entry.get())
                    slow_period = int(self.macd_slow_entry.get())
                    signal_period = int(self.macd_signal_entry.get())
                    strategy.set_parameters(fast_period=fast_period, slow_period=slow_period, signal_period=signal_period)
                elif strategy_type == "KDJ":
                    strategy = KDJStrategy()
                    k_period = int(self.kdj_period_entry.get())
                    oversold = int(self.kdj_oversold_entry.get())
                    overbought = int(self.kdj_overbought_entry.get())
                    strategy.set_parameters(k_period=k_period, oversold=oversold, overbought=overbought)
                elif strategy_type == "CUSTOM":
                    strategy = CustomStrategy("自定义策略", self.custom_strategy_code)
                else:
                    raise ValueError(f"未知的策略类型: {strategy_type}")

                # 设置策略和数据
                engine.set_strategy(strategy)
                engine.load_data(data)

                # 更新状态
                self.master.after(0, lambda: self.result_text.insert(tk.END, "正在运行回测...\n"))

                # 运行回测
                results = engine.run_backtest(ts_code)
                self.backtest_results = results

                # 显示结果
                self.master.after(0, lambda: self.display_backtest_results(engine, results))

            except Exception as e:
                self.master.after(0, lambda: messagebox.showerror("错误", f"回测失败: {str(e)}"))

        # 清空结果显示
        self.result_text.delete(1.0, tk.END)

        # 在新线程中运行回测
        thread = threading.Thread(target=backtest_thread)
        thread.daemon = True
        thread.start()

    def display_backtest_results(self, engine, results):
        """显示回测结果"""
        # 清空之前的结果
        self.result_text.delete(1.0, tk.END)

        # 显示基本结果
        self.result_text.insert(tk.END, "=" * 50 + "\n")
        self.result_text.insert(tk.END, "回测结果摘要\n")
        self.result_text.insert(tk.END, "=" * 50 + "\n")
        self.result_text.insert(tk.END, f"策略名称: {engine.strategy.name}\n")
        self.result_text.insert(tk.END, f"初始资金: ¥{results['initial_capital']:,.2f}\n")
        self.result_text.insert(tk.END, f"最终资产: ¥{results['final_value']:,.2f}\n")
        self.result_text.insert(tk.END, f"总收益率: {results['total_return']:.2%}\n")
        self.result_text.insert(tk.END, f"年化收益率: {results['annual_return']:.2%}\n")

        # 添加高收益率特殊标识
        annual_return = results.get('annual_return', 0)
        if annual_return > 1.0:  # 年化收益率超过100%
            self.result_text.insert(tk.END, "🚀 超高收益策略！年化收益率超过100%！\n")
            self.result_text.insert(tk.END, f"⭐ 收益倍数: {annual_return:.1f}倍\n")
        elif annual_return > 0.5:  # 年化收益率超过50%
            self.result_text.insert(tk.END, "🔥 优秀策略！年化收益率超过50%！\n")
        elif annual_return > 0.3:  # 年化收益率超过30%
            self.result_text.insert(tk.END, "✨ 良好策略！年化收益率超过30%！\n")

        self.result_text.insert(tk.END, f"波动率: {results['volatility']:.2%}\n")
        self.result_text.insert(tk.END, f"夏普比率: {results['sharpe_ratio']:.3f}\n")
        self.result_text.insert(tk.END, f"最大回撤: {results['max_drawdown']:.2%}\n")
        self.result_text.insert(tk.END, f"交易次数: {results['total_trades']}\n")
        self.result_text.insert(tk.END, f"胜率: {results['win_rate']:.2%}\n")
        self.result_text.insert(tk.END, "=" * 50 + "\n")

        # 绘制回测图表
        self.plot_backtest_results(results)

        # 自动启用买卖点显示并更新K线图
        if hasattr(self, 'show_signals_var'):
            self.show_signals_var.set(True)
            # 切换到股票分析选项卡并更新图表
            self.notebook.select(0)  # 选择第一个选项卡（股票分析）
            self.update_chart()

        # 调用使用者监控系统
        self.monitor_backtest_completion(results)

    def plot_backtest_results(self, results):
        """绘制回测结果图表"""
        self.bt_figure.clear()

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建子图
        ax1 = self.bt_figure.add_subplot(211)
        ax2 = self.bt_figure.add_subplot(212)

        # 资产净值曲线
        equity_df = results['equity_curve']
        if not equity_df.empty:
            ax1.plot(equity_df.index, equity_df['total_value'], label='资产净值', linewidth=2, color='blue')
            ax1.axhline(y=results['initial_capital'], color='r', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_title('资产净值曲线')
            ax1.set_ylabel('资产价值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

        # 回撤曲线
        if not equity_df.empty and 'drawdown' in equity_df.columns:
            ax2.fill_between(equity_df.index, equity_df['drawdown'], 0,
                           color='red', alpha=0.3, label='回撤')
            ax2.set_title('回撤曲线')
            ax2.set_ylabel('回撤比例')
            ax2.set_xlabel('时间')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        self.bt_figure.tight_layout()
        self.bt_canvas.draw()

    def show_detailed_analysis(self):
        """显示详细分析"""
        if self.backtest_results is None:
            messagebox.showwarning("警告", "请先运行回测")
            return

        # 创建新窗口显示详细分析
        analysis_window = tk.Toplevel(self.master)
        analysis_window.title("详细回测分析")
        analysis_window.geometry("800x600")

        # 创建文本框显示详细报告
        text_widget = scrolledtext.ScrolledText(analysis_window, wrap=tk.WORD)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 生成详细分析报告
        analyzer = BacktestAnalyzer(self.backtest_results)
        report = analyzer.generate_report()

        text_widget.insert(tk.END, report)

        # 添加按钮显示高级图表
        button_frame = tk.Frame(analysis_window)
        button_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Button(button_frame, text="显示高级图表",
                 command=lambda: analyzer.plot_comprehensive_analysis()).pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="关闭",
                 command=analysis_window.destroy).pack(side=tk.RIGHT, padx=5)

    def create_custom_strategy_widgets(self):
        """创建自定义策略编辑界面"""
        # 主框架
        main_frame = tk.Frame(self.custom_strategy_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧工具栏
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 策略模板选择
        template_frame = tk.LabelFrame(left_frame, text="策略模板", padx=10, pady=10)
        template_frame.pack(fill=tk.X, pady=5)

        tk.Label(template_frame, text="选择模板:").pack(anchor='w')
        self.template_var = tk.StringVar(value="基础模板")
        self.template_combo = ttk.Combobox(template_frame, textvariable=self.template_var,
                                         values=get_template_names(), state="readonly", width=15)
        self.template_combo.pack(fill=tk.X, pady=2)
        self.template_combo.bind('<<ComboboxSelected>>', self.load_template)

        tk.Button(template_frame, text="加载模板", command=self.load_template).pack(fill=tk.X, pady=2)

        # 策略操作
        operation_frame = tk.LabelFrame(left_frame, text="策略操作", padx=10, pady=10)
        operation_frame.pack(fill=tk.X, pady=5)

        tk.Button(operation_frame, text="验证代码", command=self.validate_strategy_code,
                 bg='lightblue').pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="保存策略", command=self.save_custom_strategy,
                 bg='lightgreen').pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="加载策略", command=self.load_custom_strategy,
                 bg='lightyellow').pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="运行回测", command=self.run_custom_backtest,
                 bg='orange').pack(fill=tk.X, pady=2)

        # 策略指标选股功能
        stock_selection_frame = tk.LabelFrame(left_frame, text="策略指标选股", padx=10, pady=10)
        stock_selection_frame.pack(fill=tk.X, pady=5)

        tk.Button(stock_selection_frame, text="指标选股", command=self.open_indicator_stock_selection,
                 bg='lightcoral').pack(fill=tk.X, pady=2)
        tk.Button(stock_selection_frame, text="选股结果", command=self.show_selection_results,
                 bg='lightsteelblue').pack(fill=tk.X, pady=2)

        # 帮助信息
        help_frame = tk.LabelFrame(left_frame, text="帮助", padx=10, pady=10)
        help_frame.pack(fill=tk.X, pady=5)

        tk.Button(help_frame, text="编写指南", command=self.show_strategy_guide).pack(fill=tk.X, pady=2)
        tk.Button(help_frame, text="技术指标", command=self.show_indicators_help).pack(fill=tk.X, pady=2)

        # 右侧代码编辑区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 代码编辑器
        code_frame = tk.LabelFrame(right_frame, text="策略代码编辑器", padx=10, pady=10)
        code_frame.pack(fill=tk.BOTH, expand=True)

        # 创建带行号的文本编辑器
        editor_frame = tk.Frame(code_frame)
        editor_frame.pack(fill=tk.BOTH, expand=True)

        # 行号显示
        self.line_numbers = tk.Text(editor_frame, width=4, padx=3, takefocus=0,
                                   border=0, state='disabled', wrap='none')
        self.line_numbers.pack(side=tk.LEFT, fill=tk.Y)

        # 代码编辑器
        self.code_editor = scrolledtext.ScrolledText(editor_frame, wrap=tk.NONE,
                                                   font=('Consolas', 10))
        self.code_editor.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 绑定事件更新行号
        self.code_editor.bind('<KeyRelease>', self.update_line_numbers)
        self.code_editor.bind('<Button-1>', self.update_line_numbers)
        self.code_editor.bind('<MouseWheel>', self.update_line_numbers)

        # 状态栏
        status_frame = tk.Frame(right_frame)
        status_frame.pack(fill=tk.X, pady=5)

        self.status_label = tk.Label(status_frame, text="就绪", relief=tk.SUNKEN, anchor='w')
        self.status_label.pack(fill=tk.X)

        # 加载默认模板
        self.load_template()

    def update_line_numbers(self, event=None):
        """更新行号显示"""
        self.line_numbers.config(state='normal')
        self.line_numbers.delete('1.0', tk.END)

        # 获取总行数
        lines = self.code_editor.get('1.0', tk.END).count('\n')
        line_numbers_string = "\n".join(str(i) for i in range(1, lines + 1))
        self.line_numbers.insert('1.0', line_numbers_string)
        self.line_numbers.config(state='disabled')

    def load_template(self, event=None):
        """加载策略模板"""
        template_name = self.template_var.get()
        template_code = STRATEGY_TEMPLATES.get(template_name, "")

        self.code_editor.delete('1.0', tk.END)
        self.code_editor.insert('1.0', template_code)
        self.update_line_numbers()
        self.status_label.config(text=f"已加载模板: {template_name}")

    def validate_strategy_code(self):
        """验证策略代码"""
        code = self.code_editor.get('1.0', tk.END)

        try:
            # 尝试编译代码
            compile(code, '<string>', 'exec')

            # 检查必要的变量
            if 'signals' not in code:
                raise ValueError("策略代码必须包含 'signals' 变量")

            self.status_label.config(text="代码验证通过", fg='green')
            messagebox.showinfo("验证成功", "策略代码语法正确！")

        except SyntaxError as e:
            error_msg = f"语法错误 (第{e.lineno}行): {e.msg}"
            self.status_label.config(text=error_msg, fg='red')
            messagebox.showerror("语法错误", error_msg)

        except Exception as e:
            error_msg = f"验证失败: {str(e)}"
            self.status_label.config(text=error_msg, fg='red')
            messagebox.showerror("验证失败", error_msg)

    def save_custom_strategy(self):
        """保存自定义策略"""
        from tkinter import filedialog

        code = self.code_editor.get('1.0', tk.END)

        file_path = filedialog.asksaveasfilename(
            defaultextension=".py",
            filetypes=[("Python files", "*.py"), ("Text files", "*.txt"), ("All files", "*.*")],
            title="保存策略代码"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)
                self.status_label.config(text=f"策略已保存到: {file_path}")
                messagebox.showinfo("保存成功", f"策略已保存到:\n{file_path}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存策略失败: {str(e)}")

    def load_custom_strategy(self):
        """加载自定义策略"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            filetypes=[("Python files", "*.py"), ("Text files", "*.txt"), ("All files", "*.*")],
            title="加载策略代码"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()

                self.code_editor.delete('1.0', tk.END)
                self.code_editor.insert('1.0', code)
                self.update_line_numbers()
                self.status_label.config(text=f"策略已加载: {file_path}")

            except Exception as e:
                messagebox.showerror("加载失败", f"加载策略失败: {str(e)}")

    def show_strategy_guide(self):
        """显示策略编写指南"""
        guide_window = tk.Toplevel(self.master)
        guide_window.title("策略编写指南")
        guide_window.geometry("800x600")

        text_widget = scrolledtext.ScrolledText(guide_window, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget.insert(tk.END, STRATEGY_GUIDE)
        text_widget.config(state='disabled')

    def show_indicators_help(self):
        """显示技术指标帮助"""
        help_window = tk.Toplevel(self.master)
        help_window.title("技术指标函数参考")
        help_window.geometry("800x600")

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, font=('Consolas', 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        help_text = '''
# 技术指标函数参考

## 趋势指标
- SMA(data, period): 简单移动平均线
  示例: ma5 = SMA(data['close'], 5)

- EMA(data, period): 指数移动平均线
  示例: ema12 = EMA(data['close'], 12)

## 动量指标
- RSI(data, period=14): 相对强弱指标 (0-100)
  示例: rsi = RSI(data['close'], 14)

- MOMENTUM(data, period=10): 动量指标
  示例: momentum = MOMENTUM(data['close'], 10)

- ROC(data, period=12): 变动率指标
  示例: roc = ROC(data['close'], 12)

## 波动性指标
- ATR(high, low, close, period=14): 平均真实波幅
  示例: atr = ATR(data['high'], data['low'], data['close'], 14)

- BOLL(data, period=20, std_dev=2): 布林带
  示例: upper, middle, lower = BOLL(data['close'], 20, 2)

## 复合指标
- MACD(data, fast=12, slow=26, signal=9): MACD指标
  示例: dif, dea, macd = MACD(data['close'], 12, 26, 9)

- KDJ(high, low, close, period=9): KDJ指标
  示例: k, d, j = KDJ(data['high'], data['low'], data['close'], 9)

- STOCH(high, low, close, k_period=14, d_period=3): 随机指标
  示例: k, d = STOCH(data['high'], data['low'], data['close'], 14, 3)

## 成交量指标
- OBV(close, volume): 能量潮指标
  示例: obv = OBV(data['close'], data['vol'])

- VWAP(high, low, close, volume): 成交量加权平均价
  示例: vwap = VWAP(data['high'], data['low'], data['close'], data['vol'])

## 其他指标
- CCI(high, low, close, period=20): 顺势指标
  示例: cci = CCI(data['high'], data['low'], data['close'], 20)

- WR(high, low, close, period=14): 威廉指标 (-100到0)
  示例: wr = WR(data['high'], data['low'], data['close'], 14)

## 使用注意事项
1. 所有指标函数返回pandas.Series对象
2. 使用pd.notna()检查数据有效性
3. 指标计算需要足够的历史数据
4. 注意数组索引边界
        '''

        text_widget.insert(tk.END, help_text)
        text_widget.config(state='disabled')

    def run_custom_backtest(self):
        """运行自定义策略回测"""
        # 首先验证代码
        code = self.code_editor.get('1.0', tk.END)
        if not code.strip():
            messagebox.showwarning("警告", "请先编写策略代码")
            return

        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            messagebox.showerror("代码错误", f"语法错误 (第{e.lineno}行): {e.msg}")
            return

        # 切换到回测选项卡
        self.notebook.select(1)  # 选择回测选项卡

        # 设置为自定义策略
        self.strategy_var.set("CUSTOM")

        # 保存自定义策略代码
        self.custom_strategy_code = code

        # 运行回测
        self.run_backtest()

    def open_indicator_stock_selection(self):
        """打开策略指标选股窗口"""
        selection_window = tk.Toplevel(self.master)
        selection_window.title("策略指标选股")
        selection_window.geometry("1000x700")

        # 初始化选股结果存储
        if not hasattr(self, 'stock_selection_results'):
            self.stock_selection_results = {}

        # 主框架
        main_frame = tk.Frame(selection_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧参数设置区域
        left_frame = tk.LabelFrame(main_frame, text="选股参数设置", padx=10, pady=10)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 股票池选择
        pool_frame = tk.LabelFrame(left_frame, text="股票池", padx=5, pady=5)
        pool_frame.pack(fill=tk.X, pady=5)

        self.selection_pool_var = tk.StringVar(value="沪深300")
        pool_options = ["沪深300", "中证1000", "上证50", "全部A股", "主板", "创业板", "科创板", "北交所", "自定义列表"]
        pool_combo = ttk.Combobox(pool_frame, textvariable=self.selection_pool_var,
                                 values=pool_options, state="readonly", width=15)
        pool_combo.pack(fill=tk.X, pady=2)
        pool_combo.bind('<<ComboboxSelected>>', self.on_pool_selection_changed)

        # 股票池信息显示
        self.pool_info_label = tk.Label(pool_frame, text="", font=('Arial', 8), fg='blue')
        self.pool_info_label.pack(fill=tk.X, pady=1)

        # 策略选择
        strategy_frame = tk.LabelFrame(left_frame, text="选股策略", padx=5, pady=5)
        strategy_frame.pack(fill=tk.X, pady=5)

        # 策略类型选择
        tk.Label(strategy_frame, text="策略类型:").pack(anchor='w')
        self.selection_strategy_var = tk.StringVar(value="MACD")
        strategy_options = ["MACD", "KDJ", "自定义策略"]
        strategy_combo = ttk.Combobox(strategy_frame, textvariable=self.selection_strategy_var,
                                     values=strategy_options, state="readonly", width=15)
        strategy_combo.pack(fill=tk.X, pady=2)
        strategy_combo.bind('<<ComboboxSelected>>', lambda e: self.update_strategy_params(selection_window))

        # 策略说明
        self.strategy_info_label = tk.Label(strategy_frame, text="", font=('Arial', 8), fg='blue', wraplength=200)
        self.strategy_info_label.pack(fill=tk.X, pady=2)

        # 动态策略参数区域
        self.strategy_params_frame = tk.LabelFrame(left_frame, text="策略参数", padx=5, pady=5)
        self.strategy_params_frame.pack(fill=tk.X, pady=5)

        # 时间范围
        time_frame = tk.LabelFrame(left_frame, text="时间范围", padx=5, pady=5)
        time_frame.pack(fill=tk.X, pady=5)

        # 选股日期设置
        date_input_frame = tk.Frame(time_frame)
        date_input_frame.pack(fill=tk.X, pady=2)

        tk.Label(date_input_frame, text="选股日期:").pack(side=tk.LEFT)
        self.selection_date_entry = tk.Entry(date_input_frame, width=12)
        self.selection_date_entry.pack(side=tk.LEFT, padx=5)
        self.selection_date_entry.insert(0, datetime.now().strftime("%Y%m%d"))

        # 添加实时选股按钮
        tk.Button(date_input_frame, text="实时", command=self.set_realtime_date,
                 bg='lightgreen', font=('Arial', 8)).pack(side=tk.LEFT, padx=2)

        tk.Label(time_frame, text="数据回看天数:").pack(anchor='w')
        self.lookback_days_entry = tk.Entry(time_frame, width=15)
        self.lookback_days_entry.pack(fill=tk.X, pady=2)
        self.lookback_days_entry.insert(0, "30")

        # 操作按钮
        button_frame = tk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=10)

        tk.Button(button_frame, text="开始选股", command=lambda: self.run_stock_selection(selection_window),
                 bg='lightgreen', font=('Arial', 10, 'bold')).pack(fill=tk.X, pady=2)
        tk.Button(button_frame, text="导出结果", command=self.export_selection_results,
                 bg='lightblue').pack(fill=tk.X, pady=2)
        tk.Button(button_frame, text="应用到多股票回测", command=self.apply_to_multi_backtest,
                 bg='orange').pack(fill=tk.X, pady=2)

        # 右侧结果显示区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 结果表格
        result_frame = tk.LabelFrame(right_frame, text="选股结果", padx=10, pady=10)
        result_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ('排名', '股票代码', '股票名称', '策略信号', '信号强度', '最新价格', '涨跌幅', '信号日期')
        self.selection_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.selection_tree.heading(col, text=col)
            if col == '股票代码':
                self.selection_tree.column(col, width=100, anchor='center')
            elif col == '股票名称':
                self.selection_tree.column(col, width=120, anchor='center')
            elif col == '策略信号':
                self.selection_tree.column(col, width=100, anchor='center')
            elif col == '信号日期':
                self.selection_tree.column(col, width=100, anchor='center')
            else:
                self.selection_tree.column(col, width=80, anchor='center')

        # 添加滚动条
        selection_scrollbar = ttk.Scrollbar(result_frame, orient='vertical', command=self.selection_tree.yview)
        self.selection_tree.configure(yscrollcommand=selection_scrollbar.set)

        # 布局表格和滚动条
        self.selection_tree.pack(side='left', fill=tk.BOTH, expand=True)
        selection_scrollbar.pack(side='right', fill='y')

        # 进度显示
        progress_frame = tk.Frame(right_frame)
        progress_frame.pack(fill=tk.X, pady=5)

        tk.Label(progress_frame, text="选股进度:").pack(side=tk.LEFT)
        self.selection_progress = ttk.Progressbar(progress_frame, mode='determinate')
        self.selection_progress.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.selection_status_label = tk.Label(progress_frame, text="就绪", width=20)
        self.selection_status_label.pack(side=tk.RIGHT)

        # 初始化策略参数界面
        self.update_strategy_params(selection_window)

        # 初始化股票池信息
        self.update_pool_info()

    def update_strategy_params(self, parent_window):
        """根据选择的策略类型更新参数界面"""
        # 清空现有参数控件
        for widget in self.strategy_params_frame.winfo_children():
            widget.destroy()

        strategy_type = self.selection_strategy_var.get()

        # 更新策略说明
        strategy_info_map = {
            "MACD": "使用MACD金叉作为买点信号",
            "KDJ": "使用KDJ超卖区域金叉作为买点信号",
            "自定义策略": "使用自定义策略代码的买点逻辑"
        }
        self.strategy_info_label.config(text=strategy_info_map.get(strategy_type, ""))

        if strategy_type == "MACD":
            tk.Label(self.strategy_params_frame, text="快线周期:").grid(row=0, column=0, sticky='w')
            self.macd_fast_var = tk.StringVar(value="12")
            tk.Entry(self.strategy_params_frame, textvariable=self.macd_fast_var, width=8).grid(row=0, column=1)

            tk.Label(self.strategy_params_frame, text="慢线周期:").grid(row=1, column=0, sticky='w')
            self.macd_slow_var = tk.StringVar(value="26")
            tk.Entry(self.strategy_params_frame, textvariable=self.macd_slow_var, width=8).grid(row=1, column=1)

            tk.Label(self.strategy_params_frame, text="信号周期:").grid(row=2, column=0, sticky='w')
            self.macd_signal_var = tk.StringVar(value="9")
            tk.Entry(self.strategy_params_frame, textvariable=self.macd_signal_var, width=8).grid(row=2, column=1)

        elif strategy_type == "KDJ":
            tk.Label(self.strategy_params_frame, text="K周期:").grid(row=0, column=0, sticky='w')
            self.kdj_period_var = tk.StringVar(value="9")
            tk.Entry(self.strategy_params_frame, textvariable=self.kdj_period_var, width=8).grid(row=0, column=1)

            tk.Label(self.strategy_params_frame, text="超卖线:").grid(row=1, column=0, sticky='w')
            self.kdj_oversold_var = tk.StringVar(value="20")
            tk.Entry(self.strategy_params_frame, textvariable=self.kdj_oversold_var, width=8).grid(row=1, column=1)

            tk.Label(self.strategy_params_frame, text="超买线:").grid(row=2, column=0, sticky='w')
            self.kdj_overbought_var = tk.StringVar(value="80")
            tk.Entry(self.strategy_params_frame, textvariable=self.kdj_overbought_var, width=8).grid(row=2, column=1)

        elif strategy_type == "自定义策略":
            # 检查是否有自定义策略代码
            if hasattr(self, 'custom_strategy_code') and self.custom_strategy_code.strip():
                tk.Label(self.strategy_params_frame, text="策略状态:", fg='green').grid(row=0, column=0, sticky='w')
                tk.Label(self.strategy_params_frame, text="已加载", fg='green').grid(row=0, column=1, sticky='w')

                # 显示策略代码长度
                code_length = len(self.custom_strategy_code.strip())
                tk.Label(self.strategy_params_frame, text="代码长度:").grid(row=1, column=0, sticky='w')
                tk.Label(self.strategy_params_frame, text=f"{code_length}字符").grid(row=1, column=1, sticky='w')

                # 提供编辑按钮
                tk.Button(self.strategy_params_frame, text="编辑策略",
                         command=lambda: self.notebook.select(2),  # 跳转到自定义策略页面
                         bg='lightblue', font=('Arial', 8)).grid(row=2, column=0, columnspan=2, pady=5)
            else:
                tk.Label(self.strategy_params_frame, text="策略状态:", fg='red').grid(row=0, column=0, sticky='w')
                tk.Label(self.strategy_params_frame, text="未设置", fg='red').grid(row=0, column=1, sticky='w')

                # 提供创建按钮
                tk.Button(self.strategy_params_frame, text="创建策略",
                         command=lambda: self.notebook.select(2),  # 跳转到自定义策略页面
                         bg='lightgreen', font=('Arial', 8)).grid(row=1, column=0, columnspan=2, pady=5)

    def on_pool_selection_changed(self, event=None):
        """股票池选择改变时的处理"""
        self.update_pool_info()

    def update_pool_info(self):
        """更新股票池信息显示"""
        try:
            pool_type = self.selection_pool_var.get()

            # 获取股票池信息
            pool_info_map = {
                "沪深300": "约300只大盘蓝筹股",
                "中证1000": "约1000只中小盘股",
                "上证50": "约50只超大盘股",
                "全部A股": "全市场A股股票（数千只）",
                "主板": "主板上市股票",
                "创业板": "创业板上市股票",
                "科创板": "科创板上市股票",
                "北交所": "北交所上市股票",
                "自定义列表": "使用多股票回测中的股票列表"
            }

            info_text = pool_info_map.get(pool_type, "")

            # 如果是全部A股，尝试获取实际数量
            if pool_type == "全部A股" and self.market_data_manager:
                try:
                    all_stocks = self.market_data_manager.get_all_stocks()
                    if all_stocks:
                        info_text = f"全市场A股股票（约{len(all_stocks)}只）"
                except:
                    pass
            elif pool_type == "自定义列表" and hasattr(self, 'multi_stock_list'):
                info_text = f"自定义股票列表（{len(self.multi_stock_list)}只）"

            self.pool_info_label.config(text=info_text)

        except Exception as e:
            print(f"更新股票池信息失败: {str(e)}")
            self.pool_info_label.config(text="")

    def create_multi_stock_widgets(self):
        """创建多股票回测界面"""
        # 主框架
        main_frame = tk.Frame(self.multi_stock_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 股票列表管理
        stock_list_frame = tk.LabelFrame(left_frame, text="股票列表管理", padx=10, pady=10)
        stock_list_frame.pack(fill=tk.X, pady=5)

        # 板块选择功能
        sector_select_frame = tk.LabelFrame(stock_list_frame, text="板块选择", padx=5, pady=5)
        sector_select_frame.pack(fill=tk.X, pady=2)

        # 板块类型选择
        sector_type_frame = tk.Frame(sector_select_frame)
        sector_type_frame.pack(fill=tk.X, pady=2)

        tk.Label(sector_type_frame, text="选择板块:").pack(side=tk.LEFT)
        self.multi_sector_var = tk.StringVar(value="手动添加")
        sector_options = ["手动添加", "全部股票", "主板", "中小板", "创业板", "科创板", "北交所",
                         "中证1000", "沪深300", "上证50"]
        self.multi_sector_combo = ttk.Combobox(sector_type_frame, textvariable=self.multi_sector_var,
                                              values=sector_options, state="readonly", width=12)
        self.multi_sector_combo.pack(side=tk.LEFT, padx=5)
        self.multi_sector_combo.bind('<<ComboboxSelected>>', self.on_sector_selection_changed)

        tk.Button(sector_type_frame, text="加载板块股票",
                 command=self.load_sector_stocks, bg='lightgreen').pack(side=tk.LEFT, padx=5)

        tk.Button(sector_type_frame, text="板块信息",
                 command=self.show_sector_info, bg='lightblue').pack(side=tk.LEFT, padx=2)

        # 添加股票
        add_frame = tk.Frame(stock_list_frame)
        add_frame.pack(fill=tk.X, pady=2)

        tk.Label(add_frame, text="股票代码:").pack(side=tk.LEFT)
        self.multi_stock_entry = tk.Entry(add_frame, width=12)
        self.multi_stock_entry.pack(side=tk.LEFT, padx=5)
        tk.Button(add_frame, text="添加", command=self.add_stock_to_list).pack(side=tk.LEFT)

        # 股票列表显示
        list_frame = tk.Frame(stock_list_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # 创建列表框和滚动条
        scrollbar = tk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.stock_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, height=8)
        self.stock_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.stock_listbox.yview)

        # 列表操作按钮
        list_btn_frame = tk.Frame(stock_list_frame)
        list_btn_frame.pack(fill=tk.X, pady=2)

        tk.Button(list_btn_frame, text="删除选中", command=self.remove_selected_stock).pack(side=tk.LEFT, padx=2)
        tk.Button(list_btn_frame, text="清空列表", command=self.clear_stock_list).pack(side=tk.LEFT, padx=2)
        tk.Button(list_btn_frame, text="加载预设", command=self.load_preset_stocks).pack(side=tk.LEFT, padx=2)

        # 回测参数设置
        params_frame = tk.LabelFrame(left_frame, text="回测参数", padx=10, pady=10)
        params_frame.pack(fill=tk.X, pady=5)

        # 日期设置
        tk.Label(params_frame, text="开始日期:").grid(row=0, column=0, sticky='w', pady=2)
        self.multi_start_date_entry = tk.Entry(params_frame, width=12)
        self.multi_start_date_entry.grid(row=0, column=1, pady=2)
        self.multi_start_date_entry.insert(0, "20230101")

        tk.Label(params_frame, text="结束日期:").grid(row=1, column=0, sticky='w', pady=2)
        self.multi_end_date_entry = tk.Entry(params_frame, width=12)
        self.multi_end_date_entry.grid(row=1, column=1, pady=2)
        self.multi_end_date_entry.insert(0, datetime.now().strftime("%Y%m%d"))

        # 资金设置
        tk.Label(params_frame, text="总资金:").grid(row=2, column=0, sticky='w', pady=2)
        self.multi_capital_entry = tk.Entry(params_frame, width=12)
        self.multi_capital_entry.grid(row=2, column=1, pady=2)
        self.multi_capital_entry.insert(0, "1000000")

        # 资金分配方式
        tk.Label(params_frame, text="分配方式:").grid(row=3, column=0, sticky='w', pady=2)
        self.allocation_var = tk.StringVar(value="equal")
        allocation_combo = ttk.Combobox(params_frame, textvariable=self.allocation_var,
                                      values=["equal", "custom"], state="readonly", width=10)
        allocation_combo.grid(row=3, column=1, pady=2)

        # 策略选择
        strategy_frame = tk.LabelFrame(left_frame, text="策略选择", padx=10, pady=10)
        strategy_frame.pack(fill=tk.X, pady=5)

        self.multi_strategy_var = tk.StringVar(value="MACD")
        tk.Radiobutton(strategy_frame, text="MACD策略", variable=self.multi_strategy_var,
                      value="MACD").pack(anchor='w')
        tk.Radiobutton(strategy_frame, text="KDJ策略", variable=self.multi_strategy_var,
                      value="KDJ").pack(anchor='w')
        tk.Radiobutton(strategy_frame, text="自定义策略", variable=self.multi_strategy_var,
                      value="CUSTOM").pack(anchor='w')

        # 操作按钮
        operation_frame = tk.LabelFrame(left_frame, text="操作", padx=10, pady=10)
        operation_frame.pack(fill=tk.X, pady=5)

        tk.Button(operation_frame, text="开始回测", command=self.run_multi_stock_backtest,
                 bg='lightgreen', font=('Arial', 10, 'bold')).pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="API使用状态", command=self.show_multi_api_status,
                 bg='lightcyan').pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="导出结果", command=self.export_multi_results,
                 bg='lightblue').pack(fill=tk.X, pady=2)
        tk.Button(operation_frame, text="显示图表", command=self.show_multi_charts,
                 bg='lightyellow').pack(fill=tk.X, pady=2)

        # 右侧结果显示区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建选项卡控件用于显示不同类型的结果
        self.multi_result_notebook = ttk.Notebook(right_frame)
        self.multi_result_notebook.pack(fill=tk.BOTH, expand=True)

        # 汇总结果选项卡
        summary_frame = ttk.Frame(self.multi_result_notebook)
        self.multi_result_notebook.add(summary_frame, text="汇总结果")

        # 创建结果显示文本框
        self.multi_result_text = scrolledtext.ScrolledText(summary_frame, height=20, width=80,
                                                          font=('Consolas', 9))
        self.multi_result_text.pack(fill=tk.BOTH, expand=True)

        # 详细结果选项卡（可点击的表格）
        detail_frame = ttk.Frame(self.multi_result_notebook)
        self.multi_result_notebook.add(detail_frame, text="详细结果")

        # 创建表格显示详细结果
        detail_container = tk.Frame(detail_frame)
        detail_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 表格标题
        tk.Label(detail_container, text="点击股票代码查看详细买卖点",
                font=('Arial', 10, 'bold'), fg='blue').pack(pady=5)

        # 创建Treeview表格
        columns = ('排名', '股票代码', '收益率', '夏普比率', '最大回撤', '交易次数', '资金占比')
        self.multi_detail_tree = ttk.Treeview(detail_container, columns=columns, show='headings', height=15)

        # 设置列标题
        for col in columns:
            self.multi_detail_tree.heading(col, text=col)
            if col == '股票代码':
                self.multi_detail_tree.column(col, width=100, anchor='center')
            elif col in ['收益率', '夏普比率', '最大回撤', '资金占比']:
                self.multi_detail_tree.column(col, width=80, anchor='center')
            else:
                self.multi_detail_tree.column(col, width=60, anchor='center')

        # 添加滚动条
        detail_scrollbar = ttk.Scrollbar(detail_container, orient='vertical', command=self.multi_detail_tree.yview)
        self.multi_detail_tree.configure(yscrollcommand=detail_scrollbar.set)

        # 布局表格和滚动条
        self.multi_detail_tree.pack(side='left', fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side='right', fill='y')

        # 绑定双击事件
        self.multi_detail_tree.bind('<Double-1>', self.on_stock_double_click)

        # 进度显示
        progress_frame = tk.Frame(right_frame)
        progress_frame.pack(fill=tk.X, pady=5)

        # 进度条标签和百分比显示
        progress_label_frame = tk.Frame(progress_frame)
        progress_label_frame.pack(fill=tk.X, pady=2)

        tk.Label(progress_label_frame, text="回测进度:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        self.multi_progress_percent_label = tk.Label(progress_label_frame, text="0%", font=('Arial', 9, 'bold'), fg='blue')
        self.multi_progress_percent_label.pack(side=tk.RIGHT)

        # 进度条
        self.multi_progress = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.multi_progress.pack(fill=tk.X, pady=2)

        # 状态信息显示
        status_frame = tk.Frame(progress_frame)
        status_frame.pack(fill=tk.X, pady=2)

        tk.Label(status_frame, text="当前状态:", font=('Arial', 9)).pack(side=tk.LEFT)
        self.multi_status_label = tk.Label(status_frame, text="就绪", font=('Arial', 9), fg='green')
        self.multi_status_label.pack(side=tk.LEFT, padx=5)

        # 详细信息显示
        self.multi_detail_label = tk.Label(progress_frame, text="", font=('Arial', 8), fg='gray')
        self.multi_detail_label.pack(fill=tk.X, pady=1)

        # 初始化变量
        self.multi_stock_list = []
        self.multi_backtest_results = None

    def add_stock_to_list(self):
        """添加股票到列表"""
        stock_code = self.multi_stock_entry.get().strip().upper()
        if stock_code and stock_code not in self.multi_stock_list:
            self.multi_stock_list.append(stock_code)
            self.stock_listbox.insert(tk.END, stock_code)
            self.multi_stock_entry.delete(0, tk.END)
            self.multi_status_label.config(text=f"已添加: {stock_code}")
        elif stock_code in self.multi_stock_list:
            messagebox.showwarning("警告", f"股票 {stock_code} 已在列表中")

    def remove_selected_stock(self):
        """删除选中的股票"""
        selection = self.stock_listbox.curselection()
        if selection:
            index = selection[0]
            stock_code = self.stock_listbox.get(index)
            self.stock_listbox.delete(index)
            self.multi_stock_list.remove(stock_code)
            self.multi_status_label.config(text=f"已删除: {stock_code}")

    def clear_stock_list(self):
        """清空股票列表"""
        if messagebox.askyesno("确认", "确定要清空股票列表吗？"):
            self.stock_listbox.delete(0, tk.END)
            self.multi_stock_list.clear()
            self.multi_status_label.config(text="列表已清空")

    def load_preset_stocks(self):
        """加载预设股票列表"""
        preset_stocks = [
            "000001.SZ",  # 平安银行
            "000002.SZ",  # 万科A
            "600000.SH",  # 浦发银行
            "600036.SH",  # 招商银行
            "600519.SH",  # 贵州茅台
            "000858.SZ",  # 五粮液
            "002415.SZ",  # 海康威视
            "300059.SZ",  # 东方财富
            "300660.SZ",  # 江苏雷利
            "000776.SZ",  # 广发证券
            "000150.SZ",  # 宁德时代
            "002230.SZ",  # 科大讯飞
            "002594.SZ",  # 比亚迪
            "000651.SZ",  # 格力电器
            "600276.SH",  # 恒瑞医药
                   
            
            
        ]

        if messagebox.askyesno("确认", "加载预设股票列表？这将清空当前列表。"):
            self.clear_stock_list()
            for stock in preset_stocks:
                self.multi_stock_list.append(stock)
                self.stock_listbox.insert(tk.END, stock)
            self.multi_status_label.config(text=f"已加载 {len(preset_stocks)} 只股票")

    def on_sector_selection_changed(self, event=None):
        """板块选择改变时的处理"""
        sector = self.multi_sector_var.get()
        if sector == "手动添加":
            self.multi_stock_entry.config(state='normal')
        else:
            self.multi_stock_entry.config(state='disabled')

    def load_sector_stocks(self):
        """加载板块股票"""
        if not self.market_data_manager:
            messagebox.showerror("错误", "市场数据管理器未初始化")
            return

        sector = self.multi_sector_var.get()

        if sector == "手动添加":
            messagebox.showinfo("提示", "请手动添加股票代码")
            return

        try:
            self.multi_status_label.config(text=f"正在加载{sector}股票...")

            # 使用增强版的市场数据管理器获取板块股票
            stock_codes = self.market_data_manager.get_sector_stocks_enhanced(sector)

            if not stock_codes:
                messagebox.showerror("错误", f"未能获取{sector}的股票列表")
                return

            # 清空现有列表
            self.multi_stock_list.clear()
            self.stock_listbox.delete(0, tk.END)

            # 添加股票到列表
            for code in stock_codes:
                self.multi_stock_list.append(code)
                self.stock_listbox.insert(tk.END, code)

            self.multi_status_label.config(text=f"已加载 {len(stock_codes)} 只{sector}股票")

            # 根据股票数量给出不同的提示
            if len(stock_codes) > 500:
                messagebox.showwarning("大规模回测提示",
                    f"已加载{len(stock_codes)}只股票，这是大规模回测。\n"
                    f"预计需要较长时间，建议：\n"
                    f"1. 缩短回测时间范围\n"
                    f"2. 选择较小的股票池\n"
                    f"3. 确保网络连接稳定")
            elif len(stock_codes) > 100:
                messagebox.showinfo("中等规模回测",
                    f"已加载{len(stock_codes)}只股票，预计回测时间较长，请耐心等待。")

        except Exception as e:
            error_msg = f"加载{sector}股票失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.multi_status_label.config(text="加载失败")

    def get_sector_info(self, sector):
        """获取板块信息"""
        sector_info = {
            "全部股票": {"description": "A股市场所有股票", "expected_count": "4000+"},
            "主板": {"description": "主板市场股票", "expected_count": "1500+"},
            "中小板": {"description": "中小板市场股票", "expected_count": "500+"},
            "创业板": {"description": "创业板市场股票", "expected_count": "1000+"},
            "科创板": {"description": "科创板市场股票", "expected_count": "400+"},
            "北交所": {"description": "北京证券交易所股票", "expected_count": "100+"},
            "中证1000": {"description": "中证1000指数成分股", "expected_count": "1000"},
            "沪深300": {"description": "沪深300指数成分股", "expected_count": "300"},
            "上证50": {"description": "上证50指数成分股", "expected_count": "50"}
        }
        return sector_info.get(sector, {"description": "未知板块", "expected_count": "0"})

    def show_sector_info(self):
        """显示板块信息"""
        sector = self.multi_sector_var.get()

        if sector == "手动添加":
            messagebox.showinfo("板块信息", "手动添加模式：请逐个输入股票代码添加到回测列表。")
            return

        sector_info = self.get_sector_info(sector)

        info_text = f"""=== {sector} 板块信息 ===

📋 板块描述:
{sector_info['description']}

📊 预期股票数量:
约 {sector_info['expected_count']} 只

🔍 数据来源:
"""

        if sector in ["中证1000", "沪深300", "上证50"]:
            info_text += f"""• 指数成分股数据
• 优先使用Tushare API获取真实成分股
• API不可用时使用智能模拟算法

📈 指数特点:"""

            if sector == "中证1000":
                info_text += """
• 中小盘股票为主
• 排除沪深300成分股
• 代表中小企业成长性"""
            elif sector == "沪深300":
                info_text += """
• 大盘蓝筹股为主
• 市值加权选股
• 代表市场主流趋势"""
            elif sector == "上证50":
                info_text += """
• 上证市场龙头股
• 超大盘股为主
• 代表核心资产价值"""
        else:
            info_text += f"""• 市场数据管理器
• 基于股票代码前缀分类
• 实时更新股票列表

🏢 板块特点:"""

            if sector == "全部股票":
                info_text += """
• 包含所有A股市场股票
• 适合全市场策略验证
• 回测时间较长，建议缩短时间范围"""
            elif sector == "主板":
                info_text += """
• 传统大中型企业
• 业绩相对稳定
• 适合价值投资策略"""
            elif sector == "创业板":
                info_text += """
• 创新型企业
• 成长性较强，波动较大
• 适合成长投资策略"""
            elif sector == "科创板":
                info_text += """
• 科技创新企业
• 高成长高风险
• 适合科技主题策略"""

        info_text += f"""

⚠️ 回测建议:
• 股票数量较多时建议缩短回测时间
• 确保网络连接稳定
• 可先用小样本测试策略效果"""

        messagebox.showinfo("板块信息", info_text)

    def show_multi_api_status(self):
        """显示多股票回测的API使用状态"""
        if not self.market_data_manager:
            messagebox.showwarning("提示", "市场数据管理器未初始化")
            return

        try:
            usage_stats = self.market_data_manager.get_api_usage_stats()
            rate_limiter = usage_stats['rate_limiter']

            stock_count = len(self.multi_stock_list)

            # 估算回测所需API调用次数
            estimated_calls = stock_count  # 每只股票至少1次调用
            remaining_calls = rate_limiter['remaining_calls']

            info_text = f"""=== 多股票回测API状态 ===

📊 当前回测配置:
  股票数量: {stock_count} 只
  估算API调用: {estimated_calls} 次

🚦 API限流状态:
  当前已调用: {rate_limiter['calls_in_last_minute']}/450 次
  剩余调用次数: {remaining_calls} 次
  每次最大记录数: {rate_limiter['max_records_per_call']} 条

⚡ 回测建议:"""

            if stock_count == 0:
                info_text += "\n  ⚠️ 请先添加股票到回测列表"
            elif estimated_calls > remaining_calls:
                wait_time = 60 - (rate_limiter['calls_in_last_minute'] * 60 / 450)
                info_text += f"\n  ⚠️ API调用不足，建议等待 {wait_time:.0f} 分钟后再开始"
                info_text += f"\n  💡 或者减少股票数量到 {remaining_calls} 只以内"
            elif stock_count > 100:
                info_text += f"\n  🔶 大规模回测，预计耗时较长"
                info_text += f"\n  💡 建议使用批量获取模式（自动启用）"
            else:
                info_text += f"\n  ✅ API调用充足，可以开始回测"

            info_text += f"""

🛠️ 优化功能:
  • 自动使用批量获取（>10只股票）
  • 智能API限流控制
  • 缓存优先策略
  • 并发数自动调整（3线程）

📈 性能预估:
  小规模（<20只）: 1-2分钟
  中等规模（20-100只）: 3-10分钟
  大规模（>100只）: 10-30分钟

⚠️ 注意事项:
  • 确保网络连接稳定
  • 避免同时运行其他API密集操作
  • 大规模回测建议分批进行"""

            messagebox.showinfo("多股票回测API状态", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取API状态失败: {str(e)}")

    def run_multi_stock_backtest(self):
        """运行多股票回测"""
        if not self.multi_stock_list:
            messagebox.showwarning("警告", "请先添加股票到列表")
            return

        # 验证自定义策略
        strategy_type = self.multi_strategy_var.get()
        if strategy_type == "CUSTOM":
            if not hasattr(self, 'custom_strategy_code') or not self.custom_strategy_code.strip():
                messagebox.showwarning("警告", "请先在自定义策略页面编写策略代码")
                return

            # 验证策略代码
            try:
                compile(self.custom_strategy_code, '<string>', 'exec')
            except SyntaxError as e:
                messagebox.showerror("策略代码错误", f"语法错误 (第{e.lineno}行): {e.msg}")
                return

        def backtest_thread():
            try:
                # 获取参数
                start_date = self.multi_start_date_entry.get()
                end_date = self.multi_end_date_entry.get()
                total_capital = float(self.multi_capital_entry.get())
                allocation_method = self.allocation_var.get()

                # API状态检查和分批处理决策
                stock_count = len(self.multi_stock_list)
                batch_size = 200  # 默认批次大小

                if self.market_data_manager:
                    usage_stats = self.market_data_manager.get_api_usage_stats()
                    rate_limiter = usage_stats['rate_limiter']

                    estimated_calls = stock_count
                    remaining_calls = rate_limiter['remaining_calls']

                    self.master.after(0, lambda: self.multi_status_label.config(
                        text=f"API状态检查: {remaining_calls}/{rate_limiter['max_calls_per_minute']} 剩余调用"))

                    # 如果股票数量很大，启用分批处理
                    if stock_count > batch_size:
                        self.master.after(0, lambda: self.multi_status_label.config(
                            text=f"股票数量 {stock_count} 较大，将启用分批处理模式"))
                        print(f"启用分批处理: {stock_count} 只股票，批次大小: {batch_size}")
                    elif estimated_calls > remaining_calls:
                        # 如果API调用不足但股票数量不大，建议等待
                        self.master.after(0, lambda: self.multi_status_label.config(
                            text=f"⚠️ API调用不足，需要{estimated_calls}次，剩余{remaining_calls}次，建议稍后重试"))
                        if not messagebox.askyesno("API调用不足",
                                                 f"当前需要 {estimated_calls} 次API调用，但只剩余 {remaining_calls} 次。\n"
                                                 f"是否继续？程序将自动等待API限制重置。"):
                            return

                # 更新状态
                self.master.after(0, lambda: self.multi_status_label.config(text="初始化回测引擎..."))
                self.master.after(0, lambda: self.multi_progress.config(value=10))

                # 创建多股票回测引擎（传入市场数据管理器支持API限流）
                engine = MultiStockBacktestEngine(
                    initial_capital=total_capital,
                    commission_rate=0.001,
                    allocation_method=allocation_method,
                    market_data_manager=self.market_data_manager  # 支持API限流
                )

                # 设置数据接口（备用）
                engine.set_data_api(self.pro)

                # 添加股票列表
                engine.add_stocks(self.multi_stock_list)

                self.master.after(0, lambda: self.multi_progress.config(value=20))

                # 创建策略
                if strategy_type == "MACD":
                    strategy = MACDStrategy()
                elif strategy_type == "KDJ":
                    strategy = KDJStrategy()
                elif strategy_type == "CUSTOM":
                    # 确保策略代码是最新的
                    strategy_code = self.custom_strategy_code.strip()
                    strategy = CustomStrategy("自定义策略", strategy_code)
                    # 添加调试信息
                    self.master.after(0, lambda: self.multi_result_text.insert(tk.END,
                        f"使用自定义策略，代码长度: {len(strategy_code)} 字符\n"))
                else:
                    raise ValueError(f"未知策略类型: {strategy_type}")

                engine.set_strategy(strategy)

                self.master.after(0, lambda: self.multi_status_label.config(text="开始回测..."))
                self.master.after(0, lambda: self.multi_progress.config(value=30))

                # 创建进度回调函数
                def progress_callback(progress, message, stage):
                    # 更新进度条
                    self.master.after(0, lambda: self.multi_progress.config(value=progress))
                    # 更新百分比显示
                    self.master.after(0, lambda: self.multi_progress_percent_label.config(text=f"{progress:.1f}%"))
                    # 更新状态信息
                    self.master.after(0, lambda: self.multi_status_label.config(text=stage))
                    # 更新详细信息
                    self.master.after(0, lambda: self.multi_detail_label.config(text=message))

                # 运行回测（传入批次大小参数和进度回调）
                results = engine.run_multi_stock_backtest(start_date, end_date, max_workers=3, batch_size=batch_size, progress_callback=progress_callback)

                self.master.after(0, lambda: self.multi_progress.config(value=90))

                # 保存结果
                self.multi_backtest_results = results

                # 显示结果
                self.master.after(0, lambda: self.display_multi_stock_results(results))
                self.master.after(0, lambda: self.multi_progress.config(value=100))
                self.master.after(0, lambda: self.multi_progress_percent_label.config(text="100%"))
                self.master.after(0, lambda: self.multi_status_label.config(text="回测完成"))
                self.master.after(0, lambda: self.multi_detail_label.config(text=f"成功完成{len(results.get('stock_results', {}))}只股票的回测分析"))

            except Exception as e:
                error_msg = f"多股票回测失败: {str(e)}"
                self.master.after(0, lambda: messagebox.showerror("错误", error_msg))
                self.master.after(0, lambda: self.multi_status_label.config(text="回测失败"))
                self.master.after(0, lambda: self.multi_progress.config(value=0))

        # 清空结果显示
        self.multi_result_text.delete(1.0, tk.END)
        self.multi_progress.config(value=0)

        # 在新线程中运行回测
        thread = threading.Thread(target=backtest_thread)
        thread.daemon = True
        thread.start()

    def display_multi_stock_results(self, results):
        """显示多股票回测结果"""
        # 清空之前的结果
        self.multi_result_text.delete(1.0, tk.END)

        # 清空表格
        for item in self.multi_detail_tree.get_children():
            self.multi_detail_tree.delete(item)

        # 创建分析器
        analyzer = MultiStockAnalyzer(results)

        # 生成并显示汇总报告
        report = analyzer.generate_summary_report()
        self.multi_result_text.insert(tk.END, report)

        # 填充详细结果表格
        self.populate_detail_table(results)

        # 调用使用者监控系统（多股票回测）
        self.monitor_multi_backtest_completion(results)

    def populate_detail_table(self, results):
        """填充详细结果表格"""
        stock_results = results.get('stock_results', {})
        allocations = results.get('allocations', {})

        # 准备表格数据
        stock_performance = []
        for ts_code, result in stock_results.items():
            if 'error' not in result:
                stock_performance.append({
                    'ts_code': ts_code,
                    'return': result.get('total_return', 0),
                    'sharpe': result.get('sharpe_ratio', 0),
                    'max_dd': result.get('max_drawdown', 0),
                    'trades': result.get('total_trades', 0),
                    'allocation': allocations.get(ts_code, 0)
                })

        # 按收益率排序
        stock_performance.sort(key=lambda x: x['return'], reverse=True)

        # 插入数据到表格
        for i, stock in enumerate(stock_performance, 1):
            # 根据收益率设置颜色标签
            if stock['return'] > 0:
                tag = 'positive'
            elif stock['return'] < 0:
                tag = 'negative'
            else:
                tag = 'neutral'

            self.multi_detail_tree.insert('', 'end', values=(
                i,
                stock['ts_code'],
                f"{stock['return']:.2%}",
                f"{stock['sharpe']:.3f}",
                f"{stock['max_dd']:.2%}",
                stock['trades'],
                f"{stock['allocation']:.1%}"
            ), tags=(tag,))

        # 配置标签颜色
        self.multi_detail_tree.tag_configure('positive', foreground='green')
        self.multi_detail_tree.tag_configure('negative', foreground='red')
        self.multi_detail_tree.tag_configure('neutral', foreground='gray')

    def on_stock_double_click(self, event):
        """处理股票双击事件，跳转到详细图表"""
        selection = self.multi_detail_tree.selection()
        if not selection:
            return

        # 获取选中的股票代码
        item = self.multi_detail_tree.item(selection[0])
        values = item['values']
        if len(values) < 2:
            return

        stock_code = values[1]  # 股票代码在第二列

        try:
            # 切换到股票分析选项卡
            self.notebook.select(0)  # 选择第一个选项卡（股票分析）

            # 设置股票代码
            self.code_entry.delete(0, tk.END)
            self.code_entry.insert(0, stock_code)

            # 设置日期范围（使用多股票回测的日期）
            start_date = self.multi_start_date_entry.get()
            end_date = self.multi_end_date_entry.get()

            self.start_date_entry.delete(0, tk.END)
            self.start_date_entry.insert(0, start_date)

            self.end_date_entry.delete(0, tk.END)
            self.end_date_entry.insert(0, end_date)

            # 查询股票数据
            self.query_stock()

            # 如果有回测结果，显示对应的策略和买卖点
            if self.multi_backtest_results and 'stock_results' in self.multi_backtest_results:
                stock_results = self.multi_backtest_results['stock_results']
                if stock_code in stock_results:
                    # 设置策略类型
                    strategy_type = self.multi_strategy_var.get()
                    self.chart_strategy_var.set(strategy_type)

                    # 保存单股票回测结果用于显示买卖点
                    self.chart_strategy_results = stock_results[stock_code]

                    # 显示买卖点
                    self.show_signals_var.set(True)

                    # 更新图表
                    self.update_chart()

                    messagebox.showinfo("跳转成功",
                                      f"已跳转到股票 {stock_code} 的详细图表\n"
                                      f"策略: {strategy_type}\n"
                                      f"收益率: {values[2]}\n"
                                      f"交易次数: {values[5]}")

        except Exception as e:
            messagebox.showerror("跳转失败", f"跳转到股票详细图表失败: {str(e)}")

    def export_multi_results(self):
        """导出多股票回测结果"""
        if not self.multi_backtest_results:
            messagebox.showwarning("警告", "请先运行多股票回测")
            return

        try:
            analyzer = MultiStockAnalyzer(self.multi_backtest_results)
            filename = f"多股票回测结果_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            analyzer.export_detailed_results(filename)
            messagebox.showinfo("成功", f"结果已导出到: {filename}")
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def show_multi_charts(self):
        """显示多股票回测图表"""
        if not self.multi_backtest_results:
            messagebox.showwarning("警告", "请先运行多股票回测")
            return

        try:
            analyzer = MultiStockAnalyzer(self.multi_backtest_results)

            # 显示性能比较图
            analyzer.plot_performance_comparison()

            # 显示相关性热力图
            analyzer.plot_correlation_heatmap()

        except Exception as e:
            messagebox.showerror("错误", f"显示图表失败: {str(e)}")

    def show_trade_points(self):
        """显示买卖点详情"""
        if self.backtest_results is None:
            messagebox.showwarning("警告", "请先运行回测")
            return

        # 创建新窗口显示买卖点
        trade_window = tk.Toplevel(self.master)
        trade_window.title("买卖点详情分析")
        trade_window.geometry("1200x800")

        # 创建主框架
        main_frame = tk.Frame(trade_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧交易列表
        left_frame = tk.LabelFrame(main_frame, text="交易记录", padx=10, pady=10)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 创建交易记录表格
        columns = ('序号', '日期', '操作', '价格', '数量', '金额', '手续费')
        self.trade_tree = ttk.Treeview(left_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        for col in columns:
            self.trade_tree.heading(col, text=col)
            if col == '序号':
                self.trade_tree.column(col, width=50)
            elif col == '日期':
                self.trade_tree.column(col, width=100)
            elif col == '操作':
                self.trade_tree.column(col, width=60)
            else:
                self.trade_tree.column(col, width=80)

        # 添加滚动条
        trade_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=self.trade_tree.yview)
        self.trade_tree.configure(yscrollcommand=trade_scrollbar.set)

        self.trade_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        trade_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充交易数据
        self.populate_trade_data()

        # 右侧图表区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建图表
        self.trade_figure = plt.Figure(figsize=(8, 10), dpi=100)
        self.trade_canvas = FigureCanvasTkAgg(self.trade_figure, master=right_frame)
        self.trade_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 绘制买卖点图表
        self.plot_trade_points()

        # 底部统计信息
        stats_frame = tk.LabelFrame(trade_window, text="交易统计", padx=10, pady=10)
        stats_frame.pack(fill=tk.X, padx=10, pady=5)

        self.display_trade_statistics(stats_frame)

        # 绑定选择事件
        self.trade_tree.bind('<<TreeviewSelect>>', self.on_trade_select)

    def populate_trade_data(self):
        """填充交易数据到表格"""
        if 'trades' not in self.backtest_results:
            return

        trades_df = self.backtest_results['trades']
        if trades_df.empty:
            return

        # 清空现有数据
        for item in self.trade_tree.get_children():
            self.trade_tree.delete(item)

        # 添加交易记录
        for i, (_, trade) in enumerate(trades_df.iterrows(), 1):
            action_text = "买入" if trade['action'] == 'BUY' else "卖出"

            # 根据操作类型设置不同的标签
            tag = 'buy' if trade['action'] == 'BUY' else 'sell'

            self.trade_tree.insert('', tk.END, values=(
                i,
                trade['date'],
                action_text,
                f"{trade['price']:.2f}",
                f"{trade['quantity']:,}",
                f"{trade['value']:,.2f}",
                f"{trade['commission']:.2f}"
            ), tags=(tag,))

        # 设置标签颜色
        self.trade_tree.tag_configure('buy', background='#ffeeee')
        self.trade_tree.tag_configure('sell', background='#eeffee')

    def plot_trade_points(self):
        """绘制买卖点图表"""
        self.trade_figure.clear()

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei']
        plt.rcParams['axes.unicode_minus'] = False

        # 获取信号数据
        signal_data = self.backtest_results.get('signal_data')
        if signal_data is None or signal_data.empty:
            return

        # 创建子图
        ax1 = self.trade_figure.add_subplot(311)
        ax2 = self.trade_figure.add_subplot(312)
        ax3 = self.trade_figure.add_subplot(313)

        # 1. 股价和买卖点
        dates = signal_data.index
        ax1.plot(dates, signal_data['close'], label='收盘价', linewidth=1, color='black')

        # 标记买卖点
        buy_points = signal_data[signal_data['signal'] == 1]
        sell_points = signal_data[signal_data['signal'] == -1]

        if not buy_points.empty:
            ax1.scatter(buy_points.index, buy_points['close'],
                       color='red', marker='^', s=100, label='买入点', zorder=5)

        if not sell_points.empty:
            ax1.scatter(sell_points.index, sell_points['close'],
                       color='green', marker='v', s=100, label='卖出点', zorder=5)

        ax1.set_title('股价走势与买卖点')
        ax1.set_ylabel('股价')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 持仓状态
        if 'position' in signal_data.columns:
            ax2.fill_between(dates, signal_data['position'], 0,
                           alpha=0.3, color='blue', label='持仓状态')
            ax2.set_title('持仓状态')
            ax2.set_ylabel('持仓 (1=持有, 0=空仓)')
            ax2.set_ylim(-0.1, 1.1)
            ax2.legend()
            ax2.grid(True, alpha=0.3)

        # 3. 资产净值曲线
        equity_df = self.backtest_results.get('equity_curve')
        if equity_df is not None and not equity_df.empty:
            ax3.plot(equity_df.index, equity_df['total_value'],
                    label='资产净值', linewidth=2, color='blue')
            ax3.axhline(y=self.backtest_results['initial_capital'],
                       color='r', linestyle='--', alpha=0.7, label='初始资金')

            # 标记交易时点的净值
            trades_df = self.backtest_results.get('trades')
            if trades_df is not None and not trades_df.empty:
                for _, trade in trades_df.iterrows():
                    trade_date = trade['date']
                    # 找到对应的净值
                    try:
                        trade_idx = pd.to_datetime(trade_date)
                        if trade_idx in equity_df.index:
                            trade_value = equity_df.loc[trade_idx, 'total_value']
                            color = 'red' if trade['action'] == 'BUY' else 'green'
                            marker = '^' if trade['action'] == 'BUY' else 'v'
                            ax3.scatter(trade_idx, trade_value,
                                      color=color, marker=marker, s=50, alpha=0.7)
                    except:
                        continue

            ax3.set_title('资产净值变化')
            ax3.set_ylabel('资产价值')
            ax3.set_xlabel('时间')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        self.trade_figure.tight_layout()
        self.trade_canvas.draw()

    def display_trade_statistics(self, parent_frame):
        """显示交易统计信息"""
        trades_df = self.backtest_results.get('trades')
        if trades_df is None or trades_df.empty:
            tk.Label(parent_frame, text="无交易记录").pack()
            return

        # 计算交易统计
        buy_trades = trades_df[trades_df['action'] == 'BUY']
        sell_trades = trades_df[trades_df['action'] == 'SELL']

        # 创建统计信息框架
        stats_left = tk.Frame(parent_frame)
        stats_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        stats_right = tk.Frame(parent_frame)
        stats_right.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        # 左侧统计
        tk.Label(stats_left, text=f"总交易次数: {len(buy_trades)}", font=('Arial', 10)).pack(anchor='w')
        tk.Label(stats_left, text=f"买入次数: {len(buy_trades)}", font=('Arial', 10)).pack(anchor='w')
        tk.Label(stats_left, text=f"卖出次数: {len(sell_trades)}", font=('Arial', 10)).pack(anchor='w')

        if not buy_trades.empty:
            avg_buy_price = buy_trades['price'].mean()
            total_buy_amount = buy_trades['value'].sum()
            tk.Label(stats_left, text=f"平均买入价: ¥{avg_buy_price:.2f}", font=('Arial', 10)).pack(anchor='w')
            tk.Label(stats_left, text=f"总买入金额: ¥{total_buy_amount:,.2f}", font=('Arial', 10)).pack(anchor='w')

        # 右侧统计
        if not sell_trades.empty:
            avg_sell_price = sell_trades['price'].mean()
            total_sell_amount = sell_trades['value'].sum()
            tk.Label(stats_right, text=f"平均卖出价: ¥{avg_sell_price:.2f}", font=('Arial', 10)).pack(anchor='w')
            tk.Label(stats_right, text=f"总卖出金额: ¥{total_sell_amount:,.2f}", font=('Arial', 10)).pack(anchor='w')

        # 手续费统计
        total_commission = trades_df['commission'].sum()
        tk.Label(stats_right, text=f"总手续费: ¥{total_commission:.2f}", font=('Arial', 10)).pack(anchor='w')

        # 盈亏分析
        if len(buy_trades) > 0 and len(sell_trades) > 0:
            # 计算每笔完整交易的盈亏
            completed_trades = min(len(buy_trades), len(sell_trades))
            total_profit = 0
            winning_trades = 0

            for i in range(completed_trades):
                buy_amount = buy_trades.iloc[i]['value'] + buy_trades.iloc[i]['commission']
                sell_amount = sell_trades.iloc[i]['value'] - sell_trades.iloc[i]['commission']
                profit = sell_amount - buy_amount
                total_profit += profit
                if profit > 0:
                    winning_trades += 1

            win_rate = (winning_trades / completed_trades * 100) if completed_trades > 0 else 0
            tk.Label(stats_right, text=f"完整交易: {completed_trades}笔", font=('Arial', 10)).pack(anchor='w')
            tk.Label(stats_right, text=f"盈利交易: {winning_trades}笔 ({win_rate:.1f}%)", font=('Arial', 10)).pack(anchor='w')

    def on_trade_select(self, event):
        """处理交易记录选择事件"""
        selection = self.trade_tree.selection()
        if not selection:
            return

        # 获取选中的交易记录
        item = self.trade_tree.item(selection[0])
        values = item['values']

        if len(values) >= 7:
            # 在图表上高亮显示选中的交易点
            trade_date = values[1]
            action = values[2]
            price = float(values[3])

            # 这里可以添加高亮显示逻辑
            print(f"选中交易: {trade_date} {action} {price}")

    def calculate_chanlun_features(self):
        """计算缠论特征"""
        df = self.df.copy()

        # 1. 识别分型（顶分型和底分型）
        fenxing = self.identify_fenxing(df)

        # 2. 识别笔
        bi = self.identify_bi(df, fenxing)

        # 3. 识别线段
        xianduan = self.identify_xianduan(df, bi)

        return {
            'fenxing': fenxing,
            'bi': bi,
            'xianduan': xianduan
        }

    def identify_fenxing(self, df):
        """识别分型"""
        fenxing = []

        for i in range(1, len(df) - 1):
            high_prev = df.iloc[i-1]['high']
            high_curr = df.iloc[i]['high']
            high_next = df.iloc[i+1]['high']

            low_prev = df.iloc[i-1]['low']
            low_curr = df.iloc[i]['low']
            low_next = df.iloc[i+1]['low']

            # 顶分型：当前K线高点高于前后K线高点
            if high_curr > high_prev and high_curr > high_next:
                fenxing.append({
                    'index': i,
                    'date': df.iloc[i]['trade_date'],
                    'type': 'top',
                    'price': high_curr,
                    'strength': self.calculate_fenxing_strength(df, i, 'top')
                })

            # 底分型：当前K线低点低于前后K线低点
            elif low_curr < low_prev and low_curr < low_next:
                fenxing.append({
                    'index': i,
                    'date': df.iloc[i]['trade_date'],
                    'type': 'bottom',
                    'price': low_curr,
                    'strength': self.calculate_fenxing_strength(df, i, 'bottom')
                })

        return fenxing

    def calculate_fenxing_strength(self, df, index, fenxing_type):
        """计算分型强度"""
        # 简化的强度计算：看前后几根K线的价格差异
        window = 3
        start = max(0, index - window)
        end = min(len(df), index + window + 1)

        if fenxing_type == 'top':
            current_high = df.iloc[index]['high']
            nearby_highs = df.iloc[start:end]['high']
            strength = (current_high - nearby_highs.mean()) / nearby_highs.std() if nearby_highs.std() > 0 else 1
        else:  # bottom
            current_low = df.iloc[index]['low']
            nearby_lows = df.iloc[start:end]['low']
            strength = (nearby_lows.mean() - current_low) / nearby_lows.std() if nearby_lows.std() > 0 else 1

        return max(1, strength)  # 强度至少为1

    def identify_bi(self, df, fenxing):
        """识别笔"""
        if len(fenxing) < 2:
            return []

        bi = []
        current_fenxing = fenxing[0]

        for i in range(1, len(fenxing)):
            next_fenxing = fenxing[i]

            # 笔的条件：相邻分型类型不同，且满足一定的价格差异
            if (current_fenxing['type'] != next_fenxing['type'] and
                self.is_valid_bi(current_fenxing, next_fenxing)):

                bi.append({
                    'start': current_fenxing,
                    'end': next_fenxing,
                    'direction': 'up' if current_fenxing['type'] == 'bottom' else 'down',
                    'strength': (current_fenxing['strength'] + next_fenxing['strength']) / 2
                })
                current_fenxing = next_fenxing

        return bi

    def is_valid_bi(self, fenxing1, fenxing2):
        """判断是否构成有效的笔"""
        # 简化条件：价格差异超过一定阈值
        price_diff = abs(fenxing1['price'] - fenxing2['price'])
        avg_price = (fenxing1['price'] + fenxing2['price']) / 2

        # 价格差异至少为平均价格的1%
        return price_diff / avg_price > 0.01

    def identify_xianduan(self, df, bi):
        """识别线段"""
        if len(bi) < 3:
            return []

        xianduan = []
        current_start = bi[0]['start']
        current_direction = bi[0]['direction']

        for i in range(1, len(bi)):
            # 线段的识别逻辑：连续同方向的笔构成线段
            if bi[i]['direction'] != current_direction:
                # 方向改变，结束当前线段
                xianduan.append({
                    'start': current_start,
                    'end': bi[i-1]['end'],
                    'direction': current_direction,
                    'bi_count': i - len([x for x in xianduan if x['direction'] == current_direction])
                })
                current_start = bi[i-1]['end']
                current_direction = bi[i]['direction']

        # 添加最后一个线段
        if len(bi) > 0:
            xianduan.append({
                'start': current_start,
                'end': bi[-1]['end'],
                'direction': current_direction,
                'bi_count': len(bi) - len([x for x in xianduan if x['direction'] == current_direction])
            })

        return xianduan

    def plot_chanlun(self):
        """绘制缠论图表"""
        # 计算缠论特征
        chanlun_features = self.calculate_chanlun_features()

        # 创建主图
        ax = self.figure.add_subplot(111)

        # 绘制K线
        dates = self.df['trade_date']
        self.plot_candlestick(ax, dates)

        # 绘制分型
        self.plot_fenxing(ax, chanlun_features['fenxing'])

        # 绘制笔
        self.plot_bi(ax, chanlun_features['bi'])

        # 绘制线段
        self.plot_xianduan(ax, chanlun_features['xianduan'])

        ax.set_title('缠论分析图 (分型+笔+线段)')
        ax.grid(True, alpha=0.3)

        self.figure.tight_layout()

    def plot_fenxing(self, ax, fenxing):
        """绘制分型"""
        for fx in fenxing:
            x = fx['index']
            y = fx['price']

            if fx['type'] == 'top':
                # 顶分型用红色向下三角形
                ax.scatter(x, y, marker='v', color='red', s=50, zorder=5)
                ax.annotate('顶', (x, y), xytext=(0, 10), textcoords='offset points',
                           ha='center', va='bottom', color='red', fontsize=8)
            else:
                # 底分型用绿色向上三角形
                ax.scatter(x, y, marker='^', color='green', s=50, zorder=5)
                ax.annotate('底', (x, y), xytext=(0, -15), textcoords='offset points',
                           ha='center', va='top', color='green', fontsize=8)

    def plot_bi(self, ax, bi):
        """绘制笔"""
        for b in bi:
            start_x = b['start']['index']
            start_y = b['start']['price']
            end_x = b['end']['index']
            end_y = b['end']['price']

            # 笔用蓝色实线连接
            ax.plot([start_x, end_x], [start_y, end_y],
                   color='blue', linewidth=2, alpha=0.7, zorder=3)

    def plot_xianduan(self, ax, xianduan):
        """绘制线段"""
        for xd in xianduan:
            start_x = xd['start']['index']
            start_y = xd['start']['price']
            end_x = xd['end']['index']
            end_y = xd['end']['price']

            # 线段用紫色粗线连接
            ax.plot([start_x, end_x], [start_y, end_y],
                   color='purple', linewidth=3, alpha=0.8, zorder=4)

            # 在线段中点添加方向标识
            mid_x = (start_x + end_x) / 2
            mid_y = (start_y + end_y) / 2
            direction_text = '↑' if xd['direction'] == 'up' else '↓'
            ax.annotate(direction_text, (mid_x, mid_y),
                       ha='center', va='center', color='purple',
                       fontsize=12, weight='bold')

    def create_web_trading_widgets(self):
        """创建网页交易界面"""
        # 导入多股票监控相关模块
        try:
            from 多股票监控管理器 import MultiStockMonitor
            from 交易调度器 import TradingScheduler
            self.multi_stock_monitor_available = True
        except ImportError as e:
            print(f"警告: 多股票监控模块导入失败: {e}")
            self.multi_stock_monitor_available = False

        # 主框架
        main_frame = tk.Frame(self.web_trading_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建选项卡控件
        self.trading_notebook = ttk.Notebook(main_frame)
        self.trading_notebook.pack(fill=tk.BOTH, expand=True)

        # 单股票交易选项卡
        self.single_trading_frame = ttk.Frame(self.trading_notebook)
        self.trading_notebook.add(self.single_trading_frame, text="单股票交易")

        # 多股票监控选项卡
        if self.multi_stock_monitor_available:
            self.multi_trading_frame = ttk.Frame(self.trading_notebook)
            self.trading_notebook.add(self.multi_trading_frame, text="多股票监控")

        # 创建单股票交易界面
        self.create_single_trading_interface()

        # 创建多股票监控界面
        if self.multi_stock_monitor_available:
            self.create_multi_trading_interface()

    def create_single_trading_interface(self):
        """创建单股票交易界面"""
        # 左侧配置区域 - 使用Canvas和Scrollbar实现滚动
        left_canvas_frame = tk.Frame(self.single_trading_frame)
        left_canvas_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 创建Canvas和滚动条
        self.single_left_canvas = tk.Canvas(left_canvas_frame, width=300, highlightthickness=0)
        single_left_scrollbar = tk.Scrollbar(left_canvas_frame, orient="vertical", command=self.single_left_canvas.yview)
        self.single_scrollable_left_frame = tk.Frame(self.single_left_canvas)

        # 配置滚动
        self.single_scrollable_left_frame.bind(
            "<Configure>",
            lambda e: self.single_left_canvas.configure(scrollregion=self.single_left_canvas.bbox("all"))
        )

        self.single_left_canvas.create_window((0, 0), window=self.single_scrollable_left_frame, anchor="nw")
        self.single_left_canvas.configure(yscrollcommand=single_left_scrollbar.set)

        # 布局Canvas和滚动条
        self.single_left_canvas.pack(side="left", fill="both", expand=True)
        single_left_scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        def _on_single_mousewheel(event):
            self.single_left_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        self.single_left_canvas.bind("<MouseWheel>", _on_single_mousewheel)

        # 现在使用single_scrollable_left_frame作为左侧内容的父容器
        left_frame = self.single_scrollable_left_frame

        # 交易账户配置
        account_frame = tk.LabelFrame(left_frame, text="交易账户配置", padx=10, pady=10)
        account_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(account_frame, text="账户:").grid(row=0, column=0, sticky='w', pady=2)
        self.account_entry = tk.Entry(account_frame, width=20, show='*')
        self.account_entry.grid(row=0, column=1, pady=2, padx=(5, 0))

        tk.Label(account_frame, text="密码:").grid(row=1, column=0, sticky='w', pady=2)
        self.password_entry = tk.Entry(account_frame, width=20, show='*')
        self.password_entry.grid(row=1, column=1, pady=2, padx=(5, 0))

        # 交易参数配置
        trading_frame = tk.LabelFrame(left_frame, text="交易参数", padx=10, pady=10)
        trading_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(trading_frame, text="股票代码:").grid(row=0, column=0, sticky='w', pady=2)
        self.trade_code_entry = tk.Entry(trading_frame, width=20)
        self.trade_code_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.trade_code_entry.insert(0, "000001")

        tk.Label(trading_frame, text="交易数量:").grid(row=1, column=0, sticky='w', pady=2)
        self.trade_quantity_entry = tk.Entry(trading_frame, width=20)
        self.trade_quantity_entry.grid(row=1, column=1, pady=2, padx=(5, 0))
        self.trade_quantity_entry.insert(0, "100")

        tk.Label(trading_frame, text="价格类型:").grid(row=2, column=0, sticky='w', pady=2)
        self.price_type_var = tk.StringVar(value="market")
        price_frame = tk.Frame(trading_frame)
        price_frame.grid(row=2, column=1, sticky='w', pady=2, padx=(5, 0))
        tk.Radiobutton(price_frame, text="市价", variable=self.price_type_var, value="market").pack(side=tk.LEFT)
        tk.Radiobutton(price_frame, text="限价", variable=self.price_type_var, value="limit").pack(side=tk.LEFT)

        tk.Label(trading_frame, text="限价价格:").grid(row=3, column=0, sticky='w', pady=2)
        self.limit_price_entry = tk.Entry(trading_frame, width=20)
        self.limit_price_entry.grid(row=3, column=1, pady=2, padx=(5, 0))

        # 仓位管理配置
        position_frame = tk.LabelFrame(left_frame, text="仓位管理", padx=10, pady=10)
        position_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(position_frame, text="交易金额:").grid(row=0, column=0, sticky='w', pady=2)
        self.trade_amount_entry = tk.Entry(position_frame, width=20)
        self.trade_amount_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.trade_amount_entry.insert(0, "10000")

        tk.Label(position_frame, text="仓位比例:").grid(row=1, column=0, sticky='w', pady=2)
        self.position_ratio_var = tk.StringVar(value="0.5")
        ratio_frame = tk.Frame(position_frame)
        ratio_frame.grid(row=1, column=1, sticky='w', pady=2, padx=(5, 0))
        tk.Radiobutton(ratio_frame, text="25%", variable=self.position_ratio_var, value="0.25").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="50%", variable=self.position_ratio_var, value="0.5").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="75%", variable=self.position_ratio_var, value="0.75").pack(side=tk.LEFT)
        tk.Radiobutton(ratio_frame, text="100%", variable=self.position_ratio_var, value="1.0").pack(side=tk.LEFT)

        # 持仓信息显示
        holdings_frame = tk.LabelFrame(left_frame, text="持仓信息", padx=10, pady=10)
        holdings_frame.pack(fill=tk.X, pady=(0, 10))

        self.current_holdings_label = tk.Label(holdings_frame, text="当前持股: 查询中...", fg='blue')
        self.current_holdings_label.pack(anchor='w')

        self.available_funds_label = tk.Label(holdings_frame, text="可用资金: 查询中...", fg='blue')
        self.available_funds_label.pack(anchor='w')

        # 查询按钮
        query_button_frame = tk.Frame(holdings_frame)
        query_button_frame.pack(fill=tk.X, pady=(5, 0))

        self.query_holdings_button = tk.Button(query_button_frame, text="查询持仓",
                                              command=self.query_holdings,
                                              bg='lightcyan', width=10)
        self.query_holdings_button.pack(side=tk.LEFT, padx=(0, 5))

        self.query_funds_button = tk.Button(query_button_frame, text="查询资金",
                                           command=self.query_funds,
                                           bg='lightcyan', width=10)
        self.query_funds_button.pack(side=tk.LEFT)

        # 策略交易配置
        strategy_trading_frame = tk.LabelFrame(left_frame, text="策略交易", padx=10, pady=10)
        strategy_trading_frame.pack(fill=tk.X, pady=(0, 10))

        self.auto_trading_var = tk.BooleanVar(value=False)
        auto_trading_checkbox = tk.Checkbutton(strategy_trading_frame, text="启用自动交易",
                                              variable=self.auto_trading_var,
                                              command=self.toggle_auto_trading)
        auto_trading_checkbox.pack(anchor='w')

        # 添加说明文字
        auto_trading_note = tk.Label(strategy_trading_frame,
                                   text="注意：勾选后需要点击'开始自动交易'按钮才会开始监控",
                                   fg='gray', font=('Arial', 9))
        auto_trading_note.pack(anchor='w', pady=(2, 0))

        # 创建一个子框架来使用grid布局
        interval_frame = tk.Frame(strategy_trading_frame)
        interval_frame.pack(fill=tk.X, pady=(5, 0))

        tk.Label(interval_frame, text="监控间隔(秒):").grid(row=0, column=0, sticky='w', pady=2)
        self.monitor_interval_entry = tk.Entry(interval_frame, width=10)
        self.monitor_interval_entry.grid(row=0, column=1, pady=2, padx=(5, 0))
        self.monitor_interval_entry.insert(0, "60")

        # 操作按钮
        button_frame = tk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        self.connect_button = tk.Button(button_frame, text="连接交易网站",
                                       command=self.connect_trading_website,
                                       bg='lightblue')
        self.connect_button.pack(fill=tk.X, pady=2)

        self.manual_buy_button = tk.Button(button_frame, text="手动买入",
                                          command=self.manual_buy,
                                          bg='lightgreen')
        self.manual_buy_button.pack(fill=tk.X, pady=2)

        self.manual_sell_button = tk.Button(button_frame, text="手动卖出",
                                           command=self.manual_sell,
                                           bg='lightcoral')
        self.manual_sell_button.pack(fill=tk.X, pady=2)

        self.start_auto_button = tk.Button(button_frame, text="开始自动交易",
                                          command=self.start_auto_trading,
                                          bg='orange')
        self.start_auto_button.pack(fill=tk.X, pady=2)

        self.stop_auto_button = tk.Button(button_frame, text="停止自动交易",
                                         command=self.stop_auto_trading,
                                         bg='red', state='disabled')
        self.stop_auto_button.pack(fill=tk.X, pady=2)

        # 右侧日志和状态区域
        right_frame = tk.Frame(self.single_trading_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 添加界面说明
        info_frame = tk.LabelFrame(right_frame, text="界面说明", padx=10, pady=5)
        info_frame.pack(fill=tk.X, pady=(0, 5))

        info_text = tk.Label(info_frame,
                           text="💡 左侧配置区域支持鼠标滚轮滚动，可查看所有配置选项和按钮",
                           fg='blue', font=('Arial', 9))
        info_text.pack(anchor='w')

        # 状态显示
        status_frame = tk.LabelFrame(right_frame, text="交易状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        self.connection_status_label = tk.Label(status_frame, text="连接状态: 未连接", fg='red')
        self.connection_status_label.pack(anchor='w')

        self.auto_trading_status_label = tk.Label(status_frame, text="自动交易: 未启动", fg='gray')
        self.auto_trading_status_label.pack(anchor='w')

        self.last_signal_label = tk.Label(status_frame, text="最新信号: 无", fg='blue')
        self.last_signal_label.pack(anchor='w')

        self.position_status_label = tk.Label(status_frame, text="持仓状态: 未查询", fg='gray')
        self.position_status_label.pack(anchor='w')

        self.trading_decision_label = tk.Label(status_frame, text="交易决策: 等待信号", fg='gray')
        self.trading_decision_label.pack(anchor='w')

        # 交易日志
        log_frame = tk.LabelFrame(right_frame, text="交易日志", padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.trading_log = scrolledtext.ScrolledText(log_frame, height=20, width=60)
        self.trading_log.pack(fill=tk.BOTH, expand=True)

        # 初始化交易相关变量
        self.web_driver = None
        self.is_connected = False
        self.auto_trading_active = False
        self.trading_thread = None

        # 初始化持仓和资金信息
        self.current_holdings = {}  # {股票代码: 持股数量}
        self.available_funds = 0.0  # 可用资金
        self.last_holdings_update = None  # 最后更新时间

        # 添加初始日志
        self.log_trading_message("网页交易系统已初始化")
        if not SELENIUM_AVAILABLE:
            self.log_trading_message("警告: 未安装selenium库，请安装后使用网页交易功能")
            self.log_trading_message("安装命令: pip install selenium")

    def create_multi_trading_interface(self):
        """创建多股票监控交易界面"""
        # 导入多股票监控模块
        from 多股票监控管理器 import MultiStockMonitor
        from 交易调度器 import TradingScheduler

        # 主框架分为三部分：左侧控制、中间监控列表、右侧状态
        main_container = tk.Frame(self.multi_trading_frame)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧控制面板
        left_control_frame = tk.Frame(main_container)
        left_control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 5))

        # 股票选择区域
        stock_selection_frame = tk.LabelFrame(left_control_frame, text="股票选择", padx=10, pady=10)
        stock_selection_frame.pack(fill=tk.X, pady=(0, 10))

        # 选择方式
        tk.Label(stock_selection_frame, text="选择方式:").pack(anchor='w')
        self.multi_selection_method = tk.StringVar(value="manual")
        tk.Radiobutton(stock_selection_frame, text="手动添加", variable=self.multi_selection_method,
                      value="manual", command=self.on_selection_method_change).pack(anchor='w')
        tk.Radiobutton(stock_selection_frame, text="从股票池选择", variable=self.multi_selection_method,
                      value="pool", command=self.on_selection_method_change).pack(anchor='w')
        tk.Radiobutton(stock_selection_frame, text="从选股结果", variable=self.multi_selection_method,
                      value="selection", command=self.on_selection_method_change).pack(anchor='w')

        # 手动添加区域
        manual_frame = tk.Frame(stock_selection_frame)
        manual_frame.pack(fill=tk.X, pady=5)

        tk.Label(manual_frame, text="股票代码:").pack(anchor='w')
        self.multi_stock_code_entry = tk.Entry(manual_frame, width=15)
        self.multi_stock_code_entry.pack(fill=tk.X, pady=2)

        tk.Button(manual_frame, text="添加到监控", command=self.add_stock_to_monitor,
                 bg='lightgreen').pack(fill=tk.X, pady=2)

        # 股票池选择区域
        pool_frame = tk.Frame(stock_selection_frame)
        pool_frame.pack(fill=tk.X, pady=5)

        tk.Label(pool_frame, text="股票池:").pack(anchor='w')
        self.multi_stock_pool = tk.StringVar(value="沪深300")
        pool_combo = ttk.Combobox(pool_frame, textvariable=self.multi_stock_pool,
                                 values=["沪深300", "中证1000", "上证50", "创业板", "科创板"],
                                 state="readonly", width=12)
        pool_combo.pack(fill=tk.X, pady=2)

        tk.Button(pool_frame, text="从股票池添加", command=self.add_from_stock_pool,
                 bg='lightblue').pack(fill=tk.X, pady=2)

        # 策略配置区域
        strategy_config_frame = tk.LabelFrame(left_control_frame, text="策略配置", padx=10, pady=10)
        strategy_config_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(strategy_config_frame, text="默认策略:").pack(anchor='w')
        self.multi_default_strategy = tk.StringVar(value="MACD")
        tk.Radiobutton(strategy_config_frame, text="MACD", variable=self.multi_default_strategy,
                      value="MACD").pack(anchor='w')
        tk.Radiobutton(strategy_config_frame, text="KDJ", variable=self.multi_default_strategy,
                      value="KDJ").pack(anchor='w')
        tk.Radiobutton(strategy_config_frame, text="自定义", variable=self.multi_default_strategy,
                      value="CUSTOM").pack(anchor='w')

        # 监控控制区域
        monitor_control_frame = tk.LabelFrame(left_control_frame, text="监控控制", padx=10, pady=10)
        monitor_control_frame.pack(fill=tk.X, pady=(0, 10))

        tk.Label(monitor_control_frame, text="更新间隔(秒):").pack(anchor='w')
        self.multi_update_interval = tk.Entry(monitor_control_frame, width=10)
        self.multi_update_interval.pack(fill=tk.X, pady=2)
        self.multi_update_interval.insert(0, "30")

        self.multi_monitor_button = tk.Button(monitor_control_frame, text="开始监控",
                                            command=self.toggle_multi_monitor,
                                            bg='lightgreen')
        self.multi_monitor_button.pack(fill=tk.X, pady=5)

        # 交易控制区域
        trade_control_frame = tk.LabelFrame(left_control_frame, text="交易控制", padx=10, pady=10)
        trade_control_frame.pack(fill=tk.X, pady=(0, 10))

        self.multi_auto_trade_var = tk.BooleanVar(value=False)
        tk.Checkbutton(trade_control_frame, text="启用自动交易",
                      variable=self.multi_auto_trade_var,
                      command=self.toggle_multi_auto_trade).pack(anchor='w')

        tk.Label(trade_control_frame, text="单股票最大金额:").pack(anchor='w')
        self.multi_max_amount_entry = tk.Entry(trade_control_frame, width=15)
        self.multi_max_amount_entry.pack(fill=tk.X, pady=2)
        self.multi_max_amount_entry.insert(0, "10000")

        tk.Label(trade_control_frame, text="最大持仓股票数:").pack(anchor='w')
        self.multi_max_positions_entry = tk.Entry(trade_control_frame, width=15)
        self.multi_max_positions_entry.pack(fill=tk.X, pady=2)
        self.multi_max_positions_entry.insert(0, "10")

        # 中间监控列表区域
        middle_frame = tk.Frame(main_container)
        middle_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # 监控股票列表
        list_frame = tk.LabelFrame(middle_frame, text="监控股票列表", padx=10, pady=10)
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格显示监控股票
        columns = ('股票代码', '当前价格', '策略', '最新信号', '信号时间', '持仓', '状态')
        self.multi_monitor_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        for col in columns:
            self.multi_monitor_tree.heading(col, text=col)
            if col == '股票代码':
                self.multi_monitor_tree.column(col, width=80, anchor='center')
            elif col == '当前价格':
                self.multi_monitor_tree.column(col, width=80, anchor='center')
            elif col == '策略':
                self.multi_monitor_tree.column(col, width=60, anchor='center')
            elif col == '最新信号':
                self.multi_monitor_tree.column(col, width=80, anchor='center')
            elif col == '信号时间':
                self.multi_monitor_tree.column(col, width=120, anchor='center')
            elif col == '持仓':
                self.multi_monitor_tree.column(col, width=60, anchor='center')
            else:
                self.multi_monitor_tree.column(col, width=80, anchor='center')

        # 添加滚动条
        multi_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.multi_monitor_tree.yview)
        self.multi_monitor_tree.configure(yscrollcommand=multi_scrollbar.set)

        # 布局表格和滚动条
        self.multi_monitor_tree.pack(side='left', fill=tk.BOTH, expand=True)
        multi_scrollbar.pack(side='right', fill='y')

        # 绑定右键菜单
        self.multi_monitor_tree.bind('<Button-3>', self.show_stock_context_menu)

        # 操作按钮
        button_frame = tk.Frame(list_frame)
        button_frame.pack(fill=tk.X, pady=5)

        tk.Button(button_frame, text="移除选中", command=self.remove_selected_monitor_stock,
                 bg='lightcoral').pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="清空列表", command=self.clear_monitor_list,
                 bg='orange').pack(side=tk.LEFT, padx=2)
        tk.Button(button_frame, text="刷新状态", command=self.refresh_monitor_status,
                 bg='lightcyan').pack(side=tk.LEFT, padx=2)

        # 右侧状态和日志区域
        right_status_frame = tk.Frame(main_container)
        right_status_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # 监控状态
        status_frame = tk.LabelFrame(right_status_frame, text="监控状态", padx=10, pady=10)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        self.multi_monitor_status_label = tk.Label(status_frame, text="监控状态: 未启动", fg='red')
        self.multi_monitor_status_label.pack(anchor='w')

        self.multi_stock_count_label = tk.Label(status_frame, text="监控股票: 0/200", fg='blue')
        self.multi_stock_count_label.pack(anchor='w')

        self.multi_signal_count_label = tk.Label(status_frame, text="待处理信号: 0", fg='blue')
        self.multi_signal_count_label.pack(anchor='w')

        self.multi_trade_status_label = tk.Label(status_frame, text="自动交易: 未启用", fg='gray')
        self.multi_trade_status_label.pack(anchor='w')

        # 性能状态
        self.multi_performance_label = tk.Label(status_frame, text="API成功率: --", fg='blue')
        self.multi_performance_label.pack(anchor='w')

        self.multi_memory_label = tk.Label(status_frame, text="内存使用: --", fg='blue')
        self.multi_memory_label.pack(anchor='w')

        # 添加性能监控按钮
        tk.Button(status_frame, text="性能报告", command=self.show_performance_report,
                 bg='lightblue').pack(fill=tk.X, pady=2)

        # 最新信号
        signal_frame = tk.LabelFrame(right_status_frame, text="最新信号", padx=10, pady=10)
        signal_frame.pack(fill=tk.X, pady=(0, 10))

        self.multi_latest_signal_text = tk.Text(signal_frame, height=8, width=30, font=('Consolas', 9))
        signal_scrollbar = tk.Scrollbar(signal_frame, command=self.multi_latest_signal_text.yview)
        self.multi_latest_signal_text.configure(yscrollcommand=signal_scrollbar.set)

        self.multi_latest_signal_text.pack(side='left', fill=tk.BOTH, expand=True)
        signal_scrollbar.pack(side='right', fill='y')

        # 交易日志
        log_frame = tk.LabelFrame(right_status_frame, text="交易日志", padx=10, pady=10)
        log_frame.pack(fill=tk.BOTH, expand=True)

        self.multi_trading_log = scrolledtext.ScrolledText(log_frame, height=15, width=30, font=('Consolas', 9))
        self.multi_trading_log.pack(fill=tk.BOTH, expand=True)

        # 初始化多股票监控相关变量
        self.multi_stock_monitor = None
        self.trading_scheduler = None
        self.multi_monitor_running = False
        self.multi_auto_trading_enabled = False
        self.monitored_stock_list = []  # 监控的股票列表

        # 添加初始日志
        self.log_multi_trading_message("多股票监控系统已初始化")
        self.log_multi_trading_message("最大支持监控200只股票")

    def log_multi_trading_message(self, message):
        """记录多股票交易日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        if hasattr(self, 'multi_trading_log'):
            self.multi_trading_log.insert(tk.END, log_entry)
            self.multi_trading_log.see(tk.END)
        print(f"多股票交易日志: {message}")

    def on_selection_method_change(self):
        """选择方式改变时的处理"""
        method = self.multi_selection_method.get()
        self.log_multi_trading_message(f"选择方式切换为: {method}")

    def add_stock_to_monitor(self):
        """添加股票到监控列表"""
        stock_code = self.multi_stock_code_entry.get().strip().upper()
        if not stock_code:
            messagebox.showwarning("警告", "请输入股票代码")
            return

        # 检查是否已在监控列表中
        for item in self.multi_monitor_tree.get_children():
            if self.multi_monitor_tree.item(item)['values'][0] == stock_code:
                messagebox.showwarning("警告", f"股票 {stock_code} 已在监控列表中")
                return

        # 检查监控数量限制
        if len(self.monitored_stock_list) >= 200:
            messagebox.showerror("错误", "已达到最大监控数量限制(200只)")
            return

        # 添加到监控列表
        strategy = self.multi_default_strategy.get()
        self.monitored_stock_list.append({
            'ts_code': stock_code,
            'strategy': strategy,
            'status': '待启动'
        })

        # 添加到表格
        self.multi_monitor_tree.insert('', 'end', values=(
            stock_code, '--', strategy, '--', '--', '0', '待启动'
        ))

        # 清空输入框
        self.multi_stock_code_entry.delete(0, tk.END)

        # 更新状态
        self.update_multi_monitor_status()
        self.log_multi_trading_message(f"已添加股票到监控: {stock_code} (策略: {strategy})")

    def add_from_stock_pool(self):
        """从股票池添加股票"""
        pool_name = self.multi_stock_pool.get()

        if not self.market_data_manager:
            messagebox.showerror("错误", "市场数据管理器未初始化")
            return

        try:
            self.log_multi_trading_message(f"正在从{pool_name}获取股票列表...")

            # 获取股票池股票
            stock_codes = self.market_data_manager.get_sector_stocks_enhanced(pool_name)

            if not stock_codes:
                messagebox.showerror("错误", f"无法获取{pool_name}的股票列表")
                return

            # 限制数量，避免一次添加过多
            max_add = min(50, 200 - len(self.monitored_stock_list))
            if len(stock_codes) > max_add:
                result = messagebox.askyesno("确认",
                    f"{pool_name}包含{len(stock_codes)}只股票，"
                    f"由于数量限制，将只添加前{max_add}只股票。是否继续？")
                if not result:
                    return
                stock_codes = stock_codes[:max_add]

            # 添加股票
            added_count = 0
            strategy = self.multi_default_strategy.get()

            for stock_code in stock_codes:
                # 检查是否已存在
                exists = False
                for item in self.multi_monitor_tree.get_children():
                    if self.multi_monitor_tree.item(item)['values'][0] == stock_code:
                        exists = True
                        break

                if not exists and len(self.monitored_stock_list) < 200:
                    self.monitored_stock_list.append({
                        'ts_code': stock_code,
                        'strategy': strategy,
                        'status': '待启动'
                    })

                    self.multi_monitor_tree.insert('', 'end', values=(
                        stock_code, '--', strategy, '--', '--', '0', '待启动'
                    ))
                    added_count += 1

            self.update_multi_monitor_status()
            self.log_multi_trading_message(f"从{pool_name}添加了{added_count}只股票到监控")

            if added_count > 0:
                messagebox.showinfo("成功", f"成功添加{added_count}只股票到监控列表")

        except Exception as e:
            error_msg = f"从股票池添加失败: {str(e)}"
            self.log_multi_trading_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def remove_selected_monitor_stock(self):
        """移除选中的监控股票"""
        selected_items = self.multi_monitor_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请选择要移除的股票")
            return

        for item in selected_items:
            stock_code = self.multi_monitor_tree.item(item)['values'][0]

            # 从监控列表中移除
            self.monitored_stock_list = [stock for stock in self.monitored_stock_list
                                       if stock['ts_code'] != stock_code]

            # 从表格中移除
            self.multi_monitor_tree.delete(item)

            # 如果监控器正在运行，从监控器中移除
            if self.multi_stock_monitor:
                self.multi_stock_monitor.remove_stock(stock_code)

            self.log_multi_trading_message(f"已移除监控股票: {stock_code}")

        self.update_multi_monitor_status()

    def clear_monitor_list(self):
        """清空监控列表"""
        if not self.monitored_stock_list:
            return

        if messagebox.askyesno("确认", "确定要清空所有监控股票吗？"):
            # 停止监控
            if self.multi_monitor_running:
                self.stop_multi_monitor()

            # 清空列表
            self.monitored_stock_list.clear()

            # 清空表格
            for item in self.multi_monitor_tree.get_children():
                self.multi_monitor_tree.delete(item)

            self.update_multi_monitor_status()
            self.log_multi_trading_message("已清空所有监控股票")

    def update_multi_monitor_status(self):
        """更新多股票监控状态显示"""
        stock_count = len(self.monitored_stock_list)
        self.multi_stock_count_label.config(text=f"监控股票: {stock_count}/200")

        if self.multi_monitor_running:
            self.multi_monitor_status_label.config(text="监控状态: 运行中", fg='green')
        else:
            self.multi_monitor_status_label.config(text="监控状态: 未启动", fg='red')

        if self.multi_auto_trading_enabled:
            self.multi_trade_status_label.config(text="自动交易: 已启用", fg='green')
        else:
            self.multi_trade_status_label.config(text="自动交易: 未启用", fg='gray')

        # 更新性能信息
        if self.multi_stock_monitor:
            try:
                metrics = self.multi_stock_monitor.get_performance_metrics()

                # API成功率
                success_rate = (1 - metrics.get('error_rate', 0)) * 100
                self.multi_performance_label.config(text=f"API成功率: {success_rate:.1f}%")

                # 内存使用
                memory_mb = metrics.get('memory_usage_mb', 0)
                self.multi_memory_label.config(text=f"内存使用: {memory_mb:.1f}MB")

            except Exception as e:
                self.multi_performance_label.config(text="API成功率: --")
                self.multi_memory_label.config(text="内存使用: --")
        else:
            self.multi_performance_label.config(text="API成功率: --")
            self.multi_memory_label.config(text="内存使用: --")

    def toggle_multi_monitor(self):
        """切换多股票监控状态"""
        if self.multi_monitor_running:
            self.stop_multi_monitor()
        else:
            self.start_multi_monitor()

    def start_multi_monitor(self):
        """启动多股票监控"""
        if not self.monitored_stock_list:
            messagebox.showwarning("警告", "请先添加要监控的股票")
            return

        if not self.market_data_manager:
            messagebox.showerror("错误", "市场数据管理器未初始化")
            return

        try:
            # 获取更新间隔
            update_interval = int(self.multi_update_interval.get())
            if update_interval < 10:
                messagebox.showwarning("警告", "更新间隔不能少于10秒")
                return
        except ValueError:
            messagebox.showerror("错误", "请输入有效的更新间隔")
            return

        try:
            # 创建多股票监控器
            from 多股票监控管理器 import MultiStockMonitor
            self.multi_stock_monitor = MultiStockMonitor(self.market_data_manager, max_stocks=200)

            # 设置回调函数
            self.multi_stock_monitor.set_callbacks(
                data_update_callback=self.on_multi_data_update,
                signal_callback=self.on_multi_signal_generated,
                error_callback=self.on_multi_monitor_error
            )

            # 添加股票到监控器
            for stock_info in self.monitored_stock_list:
                ts_code = stock_info['ts_code']
                strategy_type = stock_info['strategy']

                # 根据策略类型设置参数
                strategy_params = {}
                if strategy_type == "MACD":
                    strategy_params = {'fast_period': 12, 'slow_period': 26, 'signal_period': 9}
                elif strategy_type == "KDJ":
                    strategy_params = {'k_period': 9, 'oversold': 20, 'overbought': 80}
                elif strategy_type == "CUSTOM":
                    if hasattr(self, 'custom_strategy_code') and self.custom_strategy_code:
                        strategy_params = {'code': self.custom_strategy_code}
                    else:
                        self.log_multi_trading_message(f"警告: 股票 {ts_code} 自定义策略代码为空，跳过")
                        continue

                success = self.multi_stock_monitor.add_stock(ts_code, strategy_type, strategy_params)
                if not success:
                    self.log_multi_trading_message(f"添加股票 {ts_code} 到监控器失败")

            # 启动监控
            self.multi_stock_monitor.start_monitoring(update_interval)
            self.multi_monitor_running = True

            # 更新界面
            self.multi_monitor_button.config(text="停止监控", bg='lightcoral')
            self.update_multi_monitor_status()

            self.log_multi_trading_message(f"多股票监控已启动，监控{len(self.monitored_stock_list)}只股票")

        except Exception as e:
            error_msg = f"启动多股票监控失败: {str(e)}"
            self.log_multi_trading_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def stop_multi_monitor(self):
        """停止多股票监控"""
        try:
            if self.multi_stock_monitor:
                self.multi_stock_monitor.stop_monitoring()
                self.multi_stock_monitor = None

            self.multi_monitor_running = False

            # 更新界面
            self.multi_monitor_button.config(text="开始监控", bg='lightgreen')
            self.update_multi_monitor_status()

            # 更新表格状态
            for item in self.multi_monitor_tree.get_children():
                values = list(self.multi_monitor_tree.item(item)['values'])
                values[6] = '已停止'  # 状态列
                self.multi_monitor_tree.item(item, values=values)

            self.log_multi_trading_message("多股票监控已停止")

        except Exception as e:
            error_msg = f"停止多股票监控失败: {str(e)}"
            self.log_multi_trading_message(error_msg)

    def toggle_multi_auto_trade(self):
        """切换多股票自动交易状态"""
        if self.multi_auto_trade_var.get():
            self.start_multi_auto_trading()
        else:
            self.stop_multi_auto_trading()

    def start_multi_auto_trading(self):
        """启动多股票自动交易"""
        if not self.multi_monitor_running:
            messagebox.showwarning("警告", "请先启动多股票监控")
            self.multi_auto_trade_var.set(False)
            return

        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            self.multi_auto_trade_var.set(False)
            return

        try:
            # 创建交易调度器
            from 交易调度器 import TradingScheduler
            self.trading_scheduler = TradingScheduler(trade_executor=self.execute_trade)

            # 设置回调函数
            self.trading_scheduler.set_callbacks(
                signal_callback=self.on_trading_signal_added,
                trade_callback=self.on_trade_completed,
                error_callback=self.on_trading_error
            )

            # 设置风险控制参数
            try:
                max_amount = float(self.multi_max_amount_entry.get())
                max_positions = int(self.multi_max_positions_entry.get())

                self.trading_scheduler.update_risk_settings(
                    max_trade_amount=max_amount,
                    max_total_positions=max_positions
                )
            except ValueError:
                messagebox.showwarning("警告", "风险控制参数格式错误，使用默认值")

            # 启动调度器
            self.trading_scheduler.start_scheduler()
            self.multi_auto_trading_enabled = True

            self.update_multi_monitor_status()
            self.log_multi_trading_message("多股票自动交易已启动")

        except Exception as e:
            error_msg = f"启动自动交易失败: {str(e)}"
            self.log_multi_trading_message(error_msg)
            messagebox.showerror("错误", error_msg)
            self.multi_auto_trade_var.set(False)

    def stop_multi_auto_trading(self):
        """停止多股票自动交易"""
        try:
            if self.trading_scheduler:
                self.trading_scheduler.stop_scheduler()
                self.trading_scheduler = None

            self.multi_auto_trading_enabled = False
            self.update_multi_monitor_status()
            self.log_multi_trading_message("多股票自动交易已停止")

        except Exception as e:
            error_msg = f"停止自动交易失败: {str(e)}"
            self.log_multi_trading_message(error_msg)

    def on_multi_data_update(self, all_status):
        """多股票数据更新回调"""
        try:
            # 更新表格中的股票信息
            for item in self.multi_monitor_tree.get_children():
                stock_code = self.multi_monitor_tree.item(item)['values'][0]

                if stock_code in all_status:
                    status_info = all_status[stock_code]
                    current_price = status_info.get('current_price', 0.0)
                    last_signal = status_info.get('last_signal', 0)
                    last_signal_time = status_info.get('last_signal_time')
                    position = status_info.get('position', 0)
                    is_active = status_info.get('is_active', True)
                    error_count = status_info.get('error_count', 0)

                    # 格式化信号显示
                    signal_text = ""
                    if last_signal == 1:
                        signal_text = "买入"
                    elif last_signal == -1:
                        signal_text = "卖出"
                    else:
                        signal_text = "--"

                    # 格式化时间显示
                    time_text = ""
                    if last_signal_time:
                        time_text = last_signal_time.strftime("%H:%M:%S")
                    else:
                        time_text = "--"

                    # 确定状态
                    if error_count > 0:
                        status_text = "错误"
                    elif not is_active:
                        status_text = "暂停"
                    else:
                        status_text = "正常"

                    # 更新表格行
                    values = list(self.multi_monitor_tree.item(item)['values'])
                    values[1] = f"{current_price:.2f}" if current_price > 0 else "--"
                    values[3] = signal_text
                    values[4] = time_text
                    values[5] = str(position)
                    values[6] = status_text

                    self.multi_monitor_tree.item(item, values=values)

                    # 根据状态设置行颜色
                    if error_count > 0:
                        self.multi_monitor_tree.item(item, tags=('error',))
                    elif last_signal != 0:
                        self.multi_monitor_tree.item(item, tags=('signal',))
                    else:
                        self.multi_monitor_tree.item(item, tags=('normal',))

            # 配置标签颜色
            self.multi_monitor_tree.tag_configure('error', background='#ffcccc')
            self.multi_monitor_tree.tag_configure('signal', background='#ccffcc')
            self.multi_monitor_tree.tag_configure('normal', background='white')

        except Exception as e:
            self.log_multi_trading_message(f"更新数据显示失败: {str(e)}")

    def on_multi_signal_generated(self, signals):
        """多股票信号生成回调"""
        try:
            for signal_data in signals:
                ts_code = signal_data['ts_code']
                signal = signal_data['signal']
                price = signal_data['price']
                signal_time = signal_data['time']
                strategy_type = signal_data['strategy_type']

                # 记录信号
                signal_text = "买入" if signal == 1 else "卖出"
                time_str = signal_time.strftime("%H:%M:%S") if signal_time else ""

                log_msg = f"🔔 {ts_code} {signal_text}信号 @ {price:.2f} ({strategy_type}) {time_str}"
                self.log_multi_trading_message(log_msg)

                # 显示在最新信号区域
                signal_display = f"{time_str} {ts_code} {signal_text} @ {price:.2f}\n"
                self.multi_latest_signal_text.insert(tk.END, signal_display)
                self.multi_latest_signal_text.see(tk.END)

                # 保持信号显示区域不超过100行
                lines = self.multi_latest_signal_text.get("1.0", tk.END).split('\n')
                if len(lines) > 100:
                    self.multi_latest_signal_text.delete("1.0", "2.0")

                # 如果启用了自动交易，添加到交易调度器
                if self.multi_auto_trading_enabled and self.trading_scheduler:
                    try:
                        # 计算交易数量
                        max_amount = float(self.multi_max_amount_entry.get())
                        quantity = int(max_amount / price / 100) * 100  # 按手计算
                        quantity = max(quantity, 100)  # 最少一手

                        action = "BUY" if signal == 1 else "SELL"

                        # 添加到交易调度器
                        success = self.trading_scheduler.add_signal(
                            ts_code=ts_code,
                            action=action,
                            price=price,
                            quantity=quantity,
                            priority=1,
                            strategy_type=strategy_type
                        )

                        if success:
                            self.log_multi_trading_message(f"✓ 交易信号已加入队列: {ts_code} {action} {quantity}股")
                        else:
                            self.log_multi_trading_message(f"✗ 交易信号被拒绝: {ts_code} {action}")

                    except Exception as e:
                        self.log_multi_trading_message(f"处理交易信号失败: {str(e)}")

            # 更新信号计数
            if self.trading_scheduler:
                queue_status = self.trading_scheduler.get_queue_status()
                pending_count = queue_status.get('pending_signals', 0)
                self.multi_signal_count_label.config(text=f"待处理信号: {pending_count}")

        except Exception as e:
            self.log_multi_trading_message(f"处理信号回调失败: {str(e)}")

    def on_multi_monitor_error(self, error_msg):
        """多股票监控错误回调"""
        self.log_multi_trading_message(f"❌ 监控错误: {error_msg}")

    def on_trading_signal_added(self, signal_dict):
        """交易信号添加回调"""
        ts_code = signal_dict['ts_code']
        action = signal_dict['action']
        self.log_multi_trading_message(f"📋 交易信号已排队: {ts_code} {action}")

    def on_trade_completed(self, signal_dict):
        """交易完成回调"""
        ts_code = signal_dict['ts_code']
        action = signal_dict['action']
        quantity = signal_dict['quantity']
        price = signal_dict['price']
        status = signal_dict['status']

        if status == "COMPLETED":
            self.log_multi_trading_message(f"✅ 交易完成: {ts_code} {action} {quantity}股 @ {price:.2f}")

            # 更新持仓信息
            if self.multi_stock_monitor:
                new_position = quantity if action == "BUY" else -quantity
                self.multi_stock_monitor.update_position(ts_code, new_position, price)
        else:
            self.log_multi_trading_message(f"❌ 交易失败: {ts_code} {action} - {signal_dict.get('error_message', '')}")

    def on_trading_error(self, error_msg):
        """交易错误回调"""
        self.log_multi_trading_message(f"❌ 交易错误: {error_msg}")

    def refresh_monitor_status(self):
        """刷新监控状态"""
        if self.multi_stock_monitor:
            all_status = self.multi_stock_monitor.get_all_status()
            self.on_multi_data_update(all_status)
            self.log_multi_trading_message("监控状态已刷新")
        else:
            self.log_multi_trading_message("监控器未运行，无法刷新状态")

    def show_stock_context_menu(self, event):
        """显示股票右键菜单"""
        # 获取选中的股票
        item = self.multi_monitor_tree.selection()
        if not item:
            return

        stock_code = self.multi_monitor_tree.item(item[0])['values'][0]

        # 创建右键菜单
        context_menu = tk.Menu(self.master, tearoff=0)
        context_menu.add_command(label=f"查看 {stock_code} 详情",
                               command=lambda: self.show_stock_detail(stock_code))
        context_menu.add_command(label="暂停监控",
                               command=lambda: self.toggle_stock_monitor(stock_code, False))
        context_menu.add_command(label="恢复监控",
                               command=lambda: self.toggle_stock_monitor(stock_code, True))
        context_menu.add_separator()
        context_menu.add_command(label="移除股票",
                               command=lambda: self.remove_specific_stock(stock_code))

        # 显示菜单
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_stock_detail(self, stock_code):
        """显示股票详细信息"""
        if not self.multi_stock_monitor:
            messagebox.showwarning("警告", "监控器未运行")
            return

        all_status = self.multi_stock_monitor.get_all_status()
        if stock_code not in all_status:
            messagebox.showwarning("警告", f"未找到股票 {stock_code} 的监控信息")
            return

        status_info = all_status[stock_code]

        # 创建详情窗口
        detail_window = tk.Toplevel(self.master)
        detail_window.title(f"股票详情 - {stock_code}")
        detail_window.geometry("400x500")
        detail_window.resizable(False, False)

        # 详情内容
        detail_text = scrolledtext.ScrolledText(detail_window, width=50, height=30, font=('Consolas', 10))
        detail_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 格式化详情信息
        detail_content = f"""股票代码: {stock_code}
策略类型: {status_info.get('strategy_type', '--')}
当前价格: {status_info.get('current_price', 0):.2f}
最新信号: {status_info.get('last_signal', 0)}
信号价格: {status_info.get('signal_price', 0):.2f}
信号时间: {status_info.get('last_signal_time', '--')}
当前持仓: {status_info.get('position', 0)}
平均成本: {status_info.get('avg_cost', 0):.2f}
监控状态: {'正常' if status_info.get('is_active', False) else '暂停'}
错误次数: {status_info.get('error_count', 0)}
最后错误: {status_info.get('last_error', '无')}
最后更新: {status_info.get('last_update_time', '--')}
"""

        detail_text.insert(tk.END, detail_content)
        detail_text.config(state=tk.DISABLED)

    def toggle_stock_monitor(self, stock_code, active):
        """切换单个股票的监控状态"""
        if self.multi_stock_monitor:
            self.multi_stock_monitor.set_stock_active(stock_code, active)
            status_text = "恢复" if active else "暂停"
            self.log_multi_trading_message(f"已{status_text}股票 {stock_code} 的监控")
            self.refresh_monitor_status()

    def remove_specific_stock(self, stock_code):
        """移除指定股票"""
        if messagebox.askyesno("确认", f"确定要移除股票 {stock_code} 吗？"):
            # 从监控器中移除
            if self.multi_stock_monitor:
                self.multi_stock_monitor.remove_stock(stock_code)

            # 从列表中移除
            self.monitored_stock_list = [stock for stock in self.monitored_stock_list
                                       if stock['ts_code'] != stock_code]

            # 从表格中移除
            for item in self.multi_monitor_tree.get_children():
                if self.multi_monitor_tree.item(item)['values'][0] == stock_code:
                    self.multi_monitor_tree.delete(item)
                    break

            self.update_multi_monitor_status()
            self.log_multi_trading_message(f"已移除股票: {stock_code}")

    def show_performance_report(self):
        """显示性能报告"""
        if not self.multi_stock_monitor:
            messagebox.showwarning("警告", "监控器未运行")
            return

        try:
            metrics = self.multi_stock_monitor.get_performance_metrics()

            # 创建报告窗口
            report_window = tk.Toplevel(self.master)
            report_window.title("性能报告")
            report_window.geometry("500x600")

            # 报告内容
            report_text = scrolledtext.ScrolledText(report_window, width=60, height=35, font=('Consolas', 10))
            report_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 格式化报告
            report_content = f"""多股票监控系统性能报告
{'='*50}

基本信息:
- 监控股票总数: {metrics.get('total_stocks', 0)}
- 活跃监控股票: {metrics.get('active_stocks', 0)}
- 错误股票数量: {metrics.get('error_stocks', 0)}
- 运行状态: {'运行中' if metrics.get('is_running', False) else '已停止'}
- 更新间隔: {metrics.get('update_interval', 0)} 秒

性能指标:
- API调用总数: {metrics.get('total_api_calls', 0)}
- API失败次数: {metrics.get('failed_api_calls', 0)}
- API成功率: {(1 - metrics.get('error_rate', 0)) * 100:.1f}%
- 平均响应时间: {metrics.get('avg_response_time', 0):.3f} 秒
- 数据更新成功率: {metrics.get('update_success_rate', 0) * 100:.1f}%

资源使用:
- 内存使用量: {metrics.get('memory_usage_mb', 0):.1f} MB
- 历史数据点: {metrics.get('total_data_points', 0)}
- 线程池大小: {metrics.get('thread_pool_size', 0)}
- 信号队列大小: {metrics.get('signal_queue_size', 0)}

建议:
"""

            # 添加建议
            if metrics.get('error_rate', 0) > 0.1:
                report_content += "- ⚠️ API错误率较高，建议检查网络连接或增加更新间隔\n"

            if metrics.get('memory_usage_mb', 0) > 100:
                report_content += "- ⚠️ 内存使用量较高，建议减少监控股票数量或清理历史数据\n"

            if metrics.get('update_success_rate', 0) < 0.8:
                report_content += "- ⚠️ 数据更新成功率较低，建议检查数据源或网络状况\n"

            if metrics.get('total_stocks', 0) > 100 and metrics.get('update_interval', 30) < 30:
                report_content += "- 💡 监控股票较多，建议增加更新间隔以提高稳定性\n"

            if metrics.get('error_rate', 0) < 0.05 and metrics.get('update_success_rate', 0) > 0.95:
                report_content += "- ✅ 系统运行良好，性能指标正常\n"

            report_text.insert(tk.END, report_content)
            report_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"生成性能报告失败: {str(e)}")

    def log_trading_message(self, message):
        """记录交易日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.trading_log.insert(tk.END, log_entry)
        self.trading_log.see(tk.END)
        print(f"交易日志: {message}")  # 同时输出到控制台

    def query_holdings(self):
        """查询当前持仓"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            return

        try:
            self.log_trading_message("正在查询持仓信息...")

            # 基于源代码：正确的持仓查询URL
            holdings_url = "https://jywg.18.cn/Search/Position"
            self.web_driver.get(holdings_url)
            self.log_trading_message(f"✓ 已导航到持仓查询页面: {holdings_url}")
            time.sleep(5)  # 等待页面加载和数据刷新

            wait = WebDriverWait(self.web_driver, 15)

            # 查找持仓表格（基于源代码：id="tabBody"）
            try:
                # 等待表格加载完成
                table_body = wait.until(EC.presence_of_element_located((By.ID, 'tabBody')))

                # 等待数据加载（避免"加载中..."状态）
                time.sleep(3)

                # 解析持仓数据
                rows = table_body.find_elements(By.TAG_NAME, 'tr')
                self.current_holdings = {}

                self.log_trading_message(f"找到 {len(rows)} 行持仓数据")

                for i, row in enumerate(rows):
                    cells = row.find_elements(By.TAG_NAME, 'td')
                    if len(cells) >= 3:
                        try:
                            # 基于源代码表格结构：证券代码、证券名称、持仓数量
                            stock_code = cells[0].text.strip()
                            stock_name = cells[1].text.strip()
                            quantity_text = cells[2].text.strip()

                            # 跳过"加载中..."等非数据行
                            if "加载中" in stock_code or "暂无" in stock_code or not stock_code:
                                continue

                            # 解析持仓数量
                            quantity = 0
                            if quantity_text and quantity_text.replace(',', '').replace('.', '').isdigit():
                                quantity = int(float(quantity_text.replace(',', '')))

                            if stock_code and quantity > 0:
                                self.current_holdings[stock_code] = {
                                    'name': stock_name,
                                    'quantity': quantity
                                }
                                self.log_trading_message(f"  - {stock_code} {stock_name}: {quantity}股")

                        except Exception as parse_error:
                            self.log_trading_message(f"⚠ 解析第{i+1}行数据失败: {str(parse_error)}")
                            continue

                # 更新显示
                current_stock = self.trade_code_entry.get()
                if current_stock in self.current_holdings:
                    holdings_info = self.current_holdings[current_stock]
                    self.current_holdings_label.config(
                        text=f"当前持股: {holdings_info['quantity']}股 ({holdings_info['name']})",
                        fg='green')
                    self.position_status_label.config(text="持仓状态: 有持仓", fg='green')
                    self.log_trading_message(f"✓ 当前股票 {current_stock} 持仓: {holdings_info['quantity']}股")
                else:
                    self.current_holdings_label.config(text=f"当前持股: 0股 ({current_stock})", fg='orange')
                    self.position_status_label.config(text="持仓状态: 空仓", fg='orange')
                    self.log_trading_message(f"✓ 当前股票 {current_stock} 无持仓")

                self.last_holdings_update = datetime.now()
                self.log_trading_message(f"✓ 持仓查询完成，共{len(self.current_holdings)}只股票有持仓")

            except Exception as e:
                self.log_trading_message(f"⚠ 持仓表格解析失败: {str(e)}")
                # 如果无法解析表格，设置为查询失败
                self.current_holdings_label.config(text="当前持股: 查询失败", fg='red')
                self.position_status_label.config(text="持仓状态: 查询失败", fg='red')

        except Exception as e:
            self.log_trading_message(f"✗ 持仓查询失败: {str(e)}")
            messagebox.showerror("查询失败", f"持仓查询失败: {str(e)}")

    def query_funds(self):
        """查询可用资金"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            return

        try:
            self.log_trading_message("正在查询资金信息...")

            # 基于源代码：使用持仓页面同时查询资金信息
            # 持仓页面包含了资金信息（总资产、可用资金等）
            funds_url = "https://jywg.18.cn/Search/Position"
            self.web_driver.get(funds_url)
            self.log_trading_message(f"✓ 已导航到资金查询页面: {funds_url}")
            time.sleep(5)  # 等待页面加载

            wait = WebDriverWait(self.web_driver, 15)

            # 查找资金信息表格（基于源代码：class="zichan"）
            try:
                # 等待资金表格加载
                funds_table = wait.until(EC.presence_of_element_located((By.CLASS_NAME, 'zichan')))
                time.sleep(2)  # 等待数据刷新

                # 解析资金信息
                rows = funds_table.find_elements(By.TAG_NAME, 'tr')
                funds_found = False

                for row in rows:
                    cells = row.find_elements(By.TAG_NAME, 'td')
                    for cell in cells:
                        cell_text = cell.text.strip()

                        # 查找可用资金
                        if "可用资金" in cell_text:
                            # 在同一个cell或相邻cell中查找金额
                            spans = cell.find_elements(By.TAG_NAME, 'span')
                            for span in spans:
                                span_text = span.text.strip()
                                if span_text and span_text != "-" and span_text != "可用资金":
                                    try:
                                        # 提取数字
                                        import re
                                        funds_match = re.search(r'[\d,]+\.?\d*', span_text)
                                        if funds_match:
                                            self.available_funds = float(funds_match.group().replace(',', ''))
                                            self.available_funds_label.config(
                                                text=f"可用资金: ¥{self.available_funds:,.2f}",
                                                fg='green')
                                            funds_found = True
                                            self.log_trading_message(f"✓ 找到可用资金: ¥{self.available_funds:,.2f}")
                                            break
                                    except:
                                        continue
                            if funds_found:
                                break

                        # 也尝试查找总资产等其他信息
                        if "总资产" in cell_text:
                            spans = cell.find_elements(By.TAG_NAME, 'span')
                            for span in spans:
                                span_text = span.text.strip()
                                if span_text and span_text != "-" and span_text != "总资产":
                                    try:
                                        import re
                                        asset_match = re.search(r'[\d,]+\.?\d*', span_text)
                                        if asset_match:
                                            total_assets = float(asset_match.group().replace(',', ''))
                                            self.log_trading_message(f"✓ 总资产: ¥{total_assets:,.2f}")
                                            break
                                    except:
                                        continue

                    if funds_found:
                        break

                if not funds_found:
                    self.available_funds_label.config(text="可用资金: 数据未加载", fg='orange')
                    self.log_trading_message("⚠ 资金数据可能还在加载中，请稍后再试")

                    # 尝试手动刷新页面数据
                    try:
                        refresh_button = self.web_driver.find_element(By.CLASS_NAME, 'refresh')
                        refresh_button.click()
                        self.log_trading_message("✓ 已点击刷新按钮，请稍后重新查询")
                    except:
                        self.log_trading_message("⚠ 无法找到刷新按钮")

            except Exception as e:
                self.log_trading_message(f"⚠ 资金信息解析失败: {str(e)}")
                self.available_funds_label.config(text="可用资金: 查询失败", fg='red')

        except Exception as e:
            self.log_trading_message(f"✗ 资金查询失败: {str(e)}")
            messagebox.showerror("查询失败", f"资金查询失败: {str(e)}")

    def connect_trading_website(self):
        """连接交易网站"""
        if not SELENIUM_AVAILABLE:
            messagebox.showerror("错误", "未安装selenium库，无法使用网页交易功能")
            return

        try:
            self.log_trading_message("正在连接交易网站...")

            # 配置Edge浏览器选项
            options = Options()
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # 启动浏览器
            self.web_driver = webdriver.Edge(options=options)
            self.web_driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 首先访问登录页面
            self.log_trading_message("正在访问登录页面...")
            self.web_driver.get('https://jywg.18.cn/Login')
            self.log_trading_message("✓ 登录页面已打开")

            # 检查是否需要手动登录
            self.log_trading_message("请在浏览器中手动完成登录操作")
            self.log_trading_message("登录成功后，系统会自动检测并跳转到交易页面")

            # 等待用户手动登录
            self.wait_for_login()

        except Exception as e:
            self.log_trading_message(f"连接失败: {str(e)}")
            messagebox.showerror("连接失败", f"无法连接到交易网站: {str(e)}")

    def wait_for_login(self):
        """等待用户手动登录"""
        def check_login():
            try:
                if self.web_driver and self.web_driver.current_url:
                    current_url = self.web_driver.current_url
                    self.log_trading_message(f"当前页面: {current_url}")

                    # 检查是否已经登录到交易页面
                    if "Trade" in current_url:
                        # 进一步检查是否有交易页面的关键元素
                        try:
                            # 检查股票代码输入框是否存在（基于源代码：id="stockCode"）
                            stock_code_input = self.web_driver.find_element(By.ID, 'stockCode')
                            if stock_code_input:
                                self.is_connected = True
                                self.connection_status_label.config(text="连接状态: 已登录", fg='green')
                                self.log_trading_message("✓ 登录成功，交易系统已就绪")
                                self.log_trading_message("✓ 检测到交易页面关键元素")
                                return
                        except:
                            self.log_trading_message("⚠ 在交易页面但未找到关键元素，继续等待...")

                    elif "Login" in current_url:
                        self.log_trading_message("⚠ 仍在登录页面，请完成登录...")

                    # 如果还没登录，继续等待
                    self.master.after(3000, check_login)  # 3秒后再次检查
            except Exception as e:
                self.log_trading_message(f"⚠ 登录检测异常: {str(e)}")
                self.master.after(3000, check_login)

        # 开始检查登录状态
        self.master.after(2000, check_login)

    def get_current_price(self):
        """获取当前股票价格"""
        try:
            if hasattr(self, 'df') and self.df is not None and not self.df.empty:
                # 使用最新的收盘价作为当前价格
                return float(self.df.iloc[-1]['close'])
            return None
        except:
            return None

    def manual_buy(self):
        """手动买入"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            return

        try:
            stock_code = self.trade_code_entry.get()
            quantity = int(self.trade_quantity_entry.get())
            price_type = self.price_type_var.get()

            if price_type == "market":
                price = self.get_current_price()
                if price is None:
                    messagebox.showerror("错误", "无法获取当前价格")
                    return
            else:
                price = float(self.limit_price_entry.get())

            self.execute_trade("BUY", stock_code, quantity, price)

        except ValueError as e:
            messagebox.showerror("错误", "请检查输入的数值格式")
        except Exception as e:
            messagebox.showerror("错误", f"买入失败: {str(e)}")

    def manual_sell(self):
        """手动卖出"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            return

        try:
            stock_code = self.trade_code_entry.get()
            quantity = int(self.trade_quantity_entry.get())
            price_type = self.price_type_var.get()

            if price_type == "market":
                price = self.get_current_price()
                if price is None:
                    messagebox.showerror("错误", "无法获取当前价格")
                    return
            else:
                price = float(self.limit_price_entry.get())

            self.execute_trade("SELL", stock_code, quantity, price)

        except ValueError as e:
            messagebox.showerror("错误", "请检查输入的数值格式")
        except Exception as e:
            messagebox.showerror("错误", f"卖出失败: {str(e)}")

    def execute_trade(self, action, stock_code, quantity, price):
        """执行交易"""
        try:
            self.log_trading_message(f"执行{action}: {stock_code}, 数量: {quantity}, 价格: {price}")

            if not self.web_driver:
                raise Exception("浏览器未连接")

            # 创建WebDriverWait对象用于等待元素
            wait = WebDriverWait(self.web_driver, 10)

            # 根据买卖方向导航到对应页面
            if action == "BUY":
                self.log_trading_message("正在导航到买入页面...")
                try:
                    # 基于源代码：买入链接 href="/Trade/Buy"
                    buy_url = "https://jywg.18.cn/Trade/Buy"
                    self.web_driver.get(buy_url)
                    self.log_trading_message("✓ 已导航到买入页面")
                    time.sleep(2)  # 等待页面加载

                except Exception as e:
                    self.log_trading_message(f"✗ 导航到买入页面失败: {str(e)}")
                    raise e

            else:  # SELL
                self.log_trading_message("正在导航到卖出页面...")
                try:
                    # 基于源代码：卖出链接 href="/Trade/Sale"
                    sell_url = "https://jywg.18.cn/Trade/Sale"
                    self.web_driver.get(sell_url)
                    self.log_trading_message("✓ 已导航到卖出页面")
                    time.sleep(2)  # 等待页面加载

                except Exception as e:
                    self.log_trading_message(f"✗ 导航到卖出页面失败: {str(e)}")
                    raise e

            time.sleep(2)  # 等待页面切换

            # 清空数据（基于源代码：id="btnReset"）
            self.log_trading_message("正在清空表单数据...")
            try:
                reset_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnReset')))
                reset_button.click()
                self.log_trading_message("✓ 表单数据清空成功")
                time.sleep(1)
            except Exception as e:
                self.log_trading_message(f"⚠ 清空数据失败，继续执行: {str(e)}")

            # 输入股票代码（基于源代码：id="stockCode"）
            self.log_trading_message(f"正在输入股票代码: {stock_code}")
            try:
                code_input = wait.until(EC.presence_of_element_located((By.ID, 'stockCode')))
                # 先清空默认值
                code_input.clear()
                time.sleep(0.5)
                code_input.send_keys(stock_code)
                self.log_trading_message("✓ 股票代码输入成功")
                time.sleep(1)

                # 等待股票名称自动填充
                try:
                    stock_name_input = self.web_driver.find_element(By.ID, 'iptbdName')
                    if stock_name_input.get_attribute('value'):
                        self.log_trading_message(f"✓ 股票名称已自动填充: {stock_name_input.get_attribute('value')}")
                except:
                    pass

            except Exception as e:
                self.log_trading_message(f"✗ 股票代码输入失败: {str(e)}")
                raise e

            # 输入价格（基于源代码：id="iptPrice"）
            self.log_trading_message(f"正在输入价格: {price}")
            try:
                price_input = wait.until(EC.element_to_be_clickable((By.ID, 'iptPrice')))
                price_input.click()
                time.sleep(0.5)
                price_input.clear()
                time.sleep(0.5)
                price_input.send_keys(str(price))
                self.log_trading_message("✓ 价格输入成功")
                time.sleep(1)
            except Exception as e:
                self.log_trading_message(f"✗ 价格输入失败: {str(e)}")
                raise e

            # 输入数量（基于源代码：id="iptCount"）
            self.log_trading_message(f"正在输入数量: {quantity}")
            try:
                amount_input = wait.until(EC.element_to_be_clickable((By.ID, 'iptCount')))
                amount_input.click()
                time.sleep(0.5)
                amount_input.clear()
                time.sleep(0.5)
                amount_input.send_keys(str(quantity))
                self.log_trading_message("✓ 数量输入成功")
                time.sleep(1)
            except Exception as e:
                self.log_trading_message(f"✗ 数量输入失败: {str(e)}")
                raise e

            # 确认交易（基于源代码：id="btnConfirm"）
            self.log_trading_message("正在提交交易...")
            try:
                # 检查按钮是否可用
                confirm_button = wait.until(EC.presence_of_element_located((By.ID, 'btnConfirm')))

                # 检查按钮状态
                if confirm_button.get_attribute('disabled'):
                    self.log_trading_message("⚠ 交易按钮被禁用，可能是参数不完整")
                    # 等待一下看是否会自动启用
                    time.sleep(2)

                # 再次尝试点击
                confirm_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnConfirm')))
                confirm_button.click()
                self.log_trading_message("✓ 交易确认按钮点击成功")
                time.sleep(3)  # 等待处理结果

            except Exception as e:
                self.log_trading_message(f"✗ 交易确认失败: {str(e)}")
                raise e

            # 处理可能出现的确认对话框
            self.log_trading_message("正在处理确认对话框...")
            try:
                # 尝试多种可能的确认按钮定位方式
                confirm_selectors = [
                    (By.XPATH, '/html/body/table/tbody/tr[2]/td[2]/div/div[1]/div[2]/div[1]/div/div[2]/button'),
                    (By.XPATH, "//button[contains(text(),'确定')]"),
                    (By.XPATH, "//button[contains(text(),'确认')]"),
                    (By.XPATH, "//button[@class='btn btn-primary']"),
                    (By.CLASS_NAME, "btn-primary")
                ]

                for by, selector in confirm_selectors:
                    try:
                        confirm_dialog = WebDriverWait(self.web_driver, 3).until(
                            EC.element_to_be_clickable((by, selector)))
                        confirm_dialog.click()
                        self.log_trading_message("✓ 确认对话框处理成功")
                        time.sleep(1)
                        break
                    except:
                        continue

            except Exception as e:
                self.log_trading_message(f"⚠ 确认对话框处理: {str(e)}")

            # 处理可能出现的返回按钮
            self.log_trading_message("正在处理返回操作...")
            try:
                return_selectors = [
                    (By.ID, 'btnCxcConfirm'),
                    (By.XPATH, "//button[contains(text(),'返回')]"),
                    (By.XPATH, "//button[contains(text(),'确定')]")
                ]

                for by, selector in return_selectors:
                    try:
                        return_button = WebDriverWait(self.web_driver, 3).until(
                            EC.element_to_be_clickable((by, selector)))
                        return_button.click()
                        self.log_trading_message("✓ 返回操作成功")
                        time.sleep(1)
                        break
                    except:
                        continue

            except Exception as e:
                self.log_trading_message(f"⚠ 返回操作: {str(e)}")

            self.log_trading_message(f"✓ {action}交易已提交")

        except Exception as e:
            self.log_trading_message(f"{action}交易失败: {str(e)}")
            raise e

    def toggle_auto_trading(self):
        """切换自动交易状态"""
        if self.auto_trading_var.get():
            self.auto_trading_status_label.config(text="自动交易: 已启用", fg='orange')
        else:
            self.auto_trading_status_label.config(text="自动交易: 已禁用", fg='gray')
            if self.auto_trading_active:
                self.stop_auto_trading()

    def start_auto_trading(self):
        """开始自动交易"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接交易网站")
            return

        if not self.auto_trading_var.get():
            messagebox.showwarning("警告", "请先启用自动交易")
            return

        # 检查是否有策略结果（图表策略或回测策略）
        has_chart_results = hasattr(self, 'chart_strategy_results') and self.chart_strategy_results is not None
        has_backtest_results = hasattr(self, 'backtest_results') and self.backtest_results is not None

        if not has_chart_results and not has_backtest_results:
            messagebox.showwarning("警告", "请先在图表分析页面或回测分析页面运行策略")
            return

        self.auto_trading_active = True
        self.start_auto_button.config(state='disabled')
        self.stop_auto_button.config(state='normal')
        self.auto_trading_status_label.config(text="自动交易: 运行中", fg='green')

        self.log_trading_message("自动交易已启动")

        # 启动交易监控线程
        self.trading_thread = threading.Thread(target=self.auto_trading_loop, daemon=True)
        self.trading_thread.start()

    def stop_auto_trading(self):
        """停止自动交易"""
        self.auto_trading_active = False
        self.start_auto_button.config(state='normal')
        self.stop_auto_button.config(state='disabled')
        self.auto_trading_status_label.config(text="自动交易: 已停止", fg='red')

        self.log_trading_message("自动交易已停止")

    def auto_trading_loop(self):
        """自动交易主循环"""
        try:
            interval = int(self.monitor_interval_entry.get())
        except:
            interval = 60

        last_signal_index = -1

        while self.auto_trading_active:
            try:
                # 检查最新的交易信号
                signal_data = self.get_latest_trading_signal()

                if signal_data and signal_data['index'] > last_signal_index:
                    signal = signal_data['signal']
                    price = signal_data['price']
                    date = signal_data['date']

                    self.master.after(0, lambda: self.last_signal_label.config(
                        text=f"最新信号: {signal} @ {price} ({date})", fg='blue'))

                    # 智能交易决策
                    self.master.after(0, lambda s=signal, p=price, d=date: self.smart_trading_decision(s, p, d))

                    last_signal_index = signal_data['index']

                # 等待下一次检查
                time.sleep(interval)

            except Exception as e:
                self.master.after(0, lambda: self.log_trading_message(f"自动交易循环错误: {str(e)}"))
                time.sleep(interval)

    def get_latest_trading_signal(self):
        """获取最新的交易信号"""
        try:
            # 优先使用图表策略结果，其次使用回测结果
            strategy_results = None
            if hasattr(self, 'chart_strategy_results') and self.chart_strategy_results is not None:
                strategy_results = self.chart_strategy_results
            elif hasattr(self, 'backtest_results') and self.backtest_results is not None:
                strategy_results = self.backtest_results

            if strategy_results is None:
                return None

            signal_data = strategy_results.get('signal_data')
            if signal_data is None or signal_data.empty:
                return None

            # 获取最新的非零信号
            signals = signal_data[signal_data['signal'] != 0]
            if signals.empty:
                return None

            latest_signal = signals.iloc[-1]

            return {
                'index': latest_signal.name,
                'signal': latest_signal['signal'],
                'price': latest_signal['close'],
                'date': latest_signal['trade_date'].strftime('%Y-%m-%d') if hasattr(latest_signal['trade_date'], 'strftime') else str(latest_signal['trade_date'])
            }

        except Exception as e:
            self.log_trading_message(f"获取交易信号失败: {str(e)}")
            return None

    def smart_trading_decision(self, signal, price, date):
        """智能交易决策"""
        try:
            stock_code = self.trade_code_entry.get()

            # 获取当前持仓状态
            current_holdings = self.current_holdings.get(stock_code, {}).get('quantity', 0)

            # 根据持仓状态和信号做出交易决策
            if signal == 1:  # 买入信号
                if current_holdings > 0:
                    # 已有持仓，买入信号 -> 不操作
                    decision = "持有"
                    reason = f"已持有{current_holdings}股，买入信号时保持持有"
                    self.trading_decision_label.config(text=f"交易决策: {decision} - {reason}", fg='blue')
                    self.log_trading_message(f"📊 买入信号但已有持仓，决策: {decision}")
                else:
                    # 空仓，买入信号 -> 买入
                    decision = "买入"
                    reason = "空仓状态，执行买入信号"
                    self.trading_decision_label.config(text=f"交易决策: {decision} - {reason}", fg='green')
                    self.log_trading_message(f"📊 买入信号且空仓，决策: {decision}")
                    self.auto_execute_buy(price)

            elif signal == -1:  # 卖出信号
                if current_holdings > 0:
                    # 有持仓，卖出信号 -> 卖出
                    decision = "卖出"
                    reason = f"持有{current_holdings}股，执行卖出信号"
                    self.trading_decision_label.config(text=f"交易决策: {decision} - {reason}", fg='red')
                    self.log_trading_message(f"📊 卖出信号且有持仓，决策: {decision}")
                    self.auto_execute_sell(price)
                else:
                    # 空仓，卖出信号 -> 不操作
                    decision = "观望"
                    reason = "空仓状态，卖出信号时保持观望"
                    self.trading_decision_label.config(text=f"交易决策: {decision} - {reason}", fg='gray')
                    self.log_trading_message(f"📊 卖出信号但空仓，决策: {decision}")
            else:
                # 无信号
                decision = "等待"
                reason = "无明确信号，等待下一个信号"
                self.trading_decision_label.config(text=f"交易决策: {decision} - {reason}", fg='gray')

        except Exception as e:
            self.log_trading_message(f"✗ 智能交易决策失败: {str(e)}")

    def calculate_trade_quantity(self, price):
        """计算交易数量"""
        try:
            # 获取交易金额和仓位比例
            trade_amount = float(self.trade_amount_entry.get())
            position_ratio = float(self.position_ratio_var.get())

            # 计算实际交易金额
            actual_amount = trade_amount * position_ratio

            # 计算可买数量（按100股整数倍）
            quantity = int(actual_amount / price / 100) * 100

            self.log_trading_message(f"💰 计算交易数量: 金额{actual_amount:.2f}, 价格{price:.2f}, 数量{quantity}股")

            return max(quantity, 100)  # 最少100股

        except Exception as e:
            self.log_trading_message(f"✗ 计算交易数量失败: {str(e)}")
            return 100  # 默认100股

    def auto_execute_buy(self, price):
        """自动执行买入"""
        try:
            stock_code = self.trade_code_entry.get()

            # 使用智能数量计算
            quantity = self.calculate_trade_quantity(price)

            self.log_trading_message(f"🔥 自动买入信号触发: {stock_code} @ {price}, 数量: {quantity}股")
            self.execute_trade("BUY", stock_code, quantity, price)

            # 买入后更新持仓信息
            self.master.after(3000, self.query_holdings)  # 3秒后查询持仓

        except Exception as e:
            self.log_trading_message(f"✗ 自动买入失败: {str(e)}")

    def auto_execute_sell(self, price):
        """自动执行卖出"""
        try:
            stock_code = self.trade_code_entry.get()

            # 卖出时使用当前持仓数量
            current_holdings = self.current_holdings.get(stock_code, {}).get('quantity', 0)
            if current_holdings > 0:
                quantity = current_holdings  # 全部卖出
            else:
                quantity = int(self.trade_quantity_entry.get())  # 使用设定数量

            self.log_trading_message(f"🔥 自动卖出信号触发: {stock_code} @ {price}, 数量: {quantity}股")
            self.execute_trade("SELL", stock_code, quantity, price)

            # 卖出后更新持仓信息
            self.master.after(3000, self.query_holdings)  # 3秒后查询持仓

        except Exception as e:
            self.log_trading_message(f"✗ 自动卖出失败: {str(e)}")

    def create_market_data_widgets(self):
        """创建市场数据管理界面"""
        if not self.market_data_manager:
            return

        # 创建主框架
        main_frame = tk.Frame(self.market_data_frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 左侧控制面板
        left_frame = tk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))

        # 数据获取控制
        data_control_frame = tk.LabelFrame(left_frame, text="数据获取", padx=10, pady=10)
        data_control_frame.pack(fill=tk.X, pady=5)

        # 获取股票列表按钮
        tk.Button(data_control_frame, text="获取股票列表",
                 command=self.get_stock_list, bg='lightgreen').pack(fill=tk.X, pady=2)

        # 批量更新数据按钮
        tk.Button(data_control_frame, text="批量更新数据",
                 command=self.batch_update_data, bg='lightblue').pack(fill=tk.X, pady=2)

        # 板块筛选
        sector_frame = tk.LabelFrame(left_frame, text="板块筛选", padx=10, pady=10)
        sector_frame.pack(fill=tk.X, pady=5)

        self.sector_var = tk.StringVar(value="全部")
        sectors = ["全部", "主板", "中小板", "创业板", "科创板", "北交所"]
        for sector in sectors:
            tk.Radiobutton(sector_frame, text=sector, variable=self.sector_var,
                          value=sector, command=self.filter_by_sector).pack(anchor='w')

        # 市场筛选
        market_frame = tk.LabelFrame(left_frame, text="市场筛选", padx=10, pady=10)
        market_frame.pack(fill=tk.X, pady=5)

        self.market_var = tk.StringVar(value="全部")
        markets = ["全部", "上海证券交易所", "深圳证券交易所", "北京证券交易所"]
        for market in markets:
            tk.Radiobutton(market_frame, text=market, variable=self.market_var,
                          value=market, command=self.filter_by_market).pack(anchor='w')

        # 搜索功能
        search_frame = tk.LabelFrame(left_frame, text="搜索股票", padx=10, pady=10)
        search_frame.pack(fill=tk.X, pady=5)

        tk.Label(search_frame, text="关键词:").pack(anchor='w')
        self.search_entry = tk.Entry(search_frame, width=20)
        self.search_entry.pack(fill=tk.X, pady=2)
        self.search_entry.bind('<Return>', lambda e: self.search_stocks())

        tk.Button(search_frame, text="搜索", command=self.search_stocks).pack(fill=tk.X, pady=2)

        # 缓存管理
        cache_frame = tk.LabelFrame(left_frame, text="缓存管理", padx=10, pady=10)
        cache_frame.pack(fill=tk.X, pady=5)

        tk.Button(cache_frame, text="查看缓存信息",
                 command=self.show_cache_info).pack(fill=tk.X, pady=2)
        tk.Button(cache_frame, text="缓存统计",
                 command=self.show_cache_stats, bg='lightblue').pack(fill=tk.X, pady=2)
        tk.Button(cache_frame, text="压缩效率分析",
                 command=self.show_compression_analysis, bg='lightgreen').pack(fill=tk.X, pady=2)
        tk.Button(cache_frame, text="API使用统计",
                 command=self.show_api_usage_stats, bg='lightcyan').pack(fill=tk.X, pady=2)
        tk.Button(cache_frame, text="清理缓存",
                 command=self.clear_cache, bg='orange').pack(fill=tk.X, pady=2)
        tk.Button(cache_frame, text="清理实时缓存",
                 command=self.clear_realtime_cache, bg='lightyellow').pack(fill=tk.X, pady=2)

        # 导出功能
        export_frame = tk.LabelFrame(left_frame, text="数据导出", padx=10, pady=10)
        export_frame.pack(fill=tk.X, pady=5)

        tk.Button(export_frame, text="导出Excel",
                 command=lambda: self.export_data('excel')).pack(fill=tk.X, pady=2)
        tk.Button(export_frame, text="导出CSV",
                 command=lambda: self.export_data('csv')).pack(fill=tk.X, pady=2)

        # 右侧数据显示区域
        right_frame = tk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建Treeview显示股票列表
        columns = ('代码', '名称', '板块', '市场', '行业', '地区', '上市日期')
        self.stock_tree = ttk.Treeview(right_frame, columns=columns, show='headings', height=20)

        # 设置列标题
        for col in columns:
            self.stock_tree.heading(col, text=col)
            self.stock_tree.column(col, width=100)

        # 添加滚动条
        scrollbar_y = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=self.stock_tree.yview)
        scrollbar_x = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=self.stock_tree.xview)
        self.stock_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # 布局
        self.stock_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定双击事件
        self.stock_tree.bind('<Double-1>', self.on_stock_double_click)

        # 状态栏
        self.status_frame = tk.Frame(self.market_data_frame)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM)

        self.status_label = tk.Label(self.status_frame, text="就绪", relief=tk.SUNKEN, anchor='w')
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.RIGHT, padx=5)

        # 初始化显示
        self.get_stock_list()

    def get_stock_list(self):
        """获取股票列表"""
        if not self.market_data_manager:
            return

        def update_ui():
            try:
                self.status_label.config(text="正在获取股票列表...")
                self.progress_var.set(50)

                # 获取股票列表
                stock_list = self.market_data_manager.get_all_stock_list()

                # 清空现有数据
                for item in self.stock_tree.get_children():
                    self.stock_tree.delete(item)

                # 添加数据到树形控件
                for _, row in stock_list.iterrows():
                    self.stock_tree.insert('', 'end', values=(
                        row['ts_code'],
                        row['name'],
                        row.get('sector', ''),
                        row.get('market_name', ''),
                        row.get('industry', ''),
                        row.get('area', ''),
                        row.get('list_date', '')
                    ))

                self.status_label.config(text=f"已加载 {len(stock_list)} 只股票")
                self.progress_var.set(100)

                # 2秒后隐藏进度条
                self.master.after(2000, lambda: self.progress_var.set(0))

            except Exception as e:
                self.status_label.config(text=f"获取股票列表失败: {str(e)}")
                messagebox.showerror("错误", f"获取股票列表失败: {str(e)}")

        # 在后台线程中执行
        threading.Thread(target=update_ui, daemon=True).start()

    def batch_update_data(self):
        """批量更新数据"""
        if not self.market_data_manager:
            return

        # 确认对话框
        if not messagebox.askyesno("确认", "批量更新数据可能需要较长时间，是否继续？"):
            return

        def update_callback(current, total, ts_code, success, error):
            """更新进度回调"""
            progress = (current / total) * 100
            self.progress_var.set(progress)

            if success:
                status_text = f"正在更新 {current}/{total}: {ts_code} ✓"
            else:
                status_text = f"正在更新 {current}/{total}: {ts_code} ✗ ({error})"

            self.status_label.config(text=status_text)

        def update_worker():
            try:
                result = self.market_data_manager.batch_update_stock_data(callback=update_callback)

                # 更新完成
                self.status_label.config(text=f"更新完成: 成功 {result['success']}, 失败 {result['error']}")

                if result['error'] > 0:
                    error_msg = f"更新完成，但有 {result['error']} 只股票更新失败"
                    messagebox.showwarning("更新完成", error_msg)
                else:
                    messagebox.showinfo("更新完成", "所有股票数据更新成功")

            except Exception as e:
                self.status_label.config(text=f"批量更新失败: {str(e)}")
                messagebox.showerror("错误", f"批量更新失败: {str(e)}")
            finally:
                self.progress_var.set(0)

        # 在后台线程中执行
        threading.Thread(target=update_worker, daemon=True).start()

    def filter_by_sector(self):
        """按板块筛选"""
        if not self.market_data_manager:
            return

        sector = self.sector_var.get()

        try:
            if sector == "全部":
                stock_list = self.market_data_manager.get_all_stock_list()
            else:
                stock_list = self.market_data_manager.get_all_stock_list()
                stock_list = stock_list[stock_list['sector'] == sector]

            # 更新显示
            self.update_stock_display(stock_list)

        except Exception as e:
            messagebox.showerror("错误", f"筛选失败: {str(e)}")

    def filter_by_market(self):
        """按市场筛选"""
        if not self.market_data_manager:
            return

        market = self.market_var.get()

        try:
            if market == "全部":
                stock_list = self.market_data_manager.get_all_stock_list()
            else:
                stock_list = self.market_data_manager.get_all_stock_list()
                stock_list = stock_list[stock_list['market_name'] == market]

            # 更新显示
            self.update_stock_display(stock_list)

        except Exception as e:
            messagebox.showerror("错误", f"筛选失败: {str(e)}")

    def search_stocks(self):
        """搜索股票"""
        if not self.market_data_manager:
            return

        keyword = self.search_entry.get().strip()
        if not keyword:
            self.get_stock_list()
            return

        try:
            stock_list = self.market_data_manager.search_stocks(keyword)
            self.update_stock_display(stock_list)

        except Exception as e:
            messagebox.showerror("错误", f"搜索失败: {str(e)}")

    def update_stock_display(self, stock_list):
        """更新股票显示"""
        # 清空现有数据
        for item in self.stock_tree.get_children():
            self.stock_tree.delete(item)

        # 添加新数据
        for _, row in stock_list.iterrows():
            self.stock_tree.insert('', 'end', values=(
                row['ts_code'],
                row['name'],
                row.get('sector', ''),
                row.get('market_name', ''),
                row.get('industry', ''),
                row.get('area', ''),
                row.get('list_date', '')
            ))

        self.status_label.config(text=f"显示 {len(stock_list)} 只股票")

    def show_cache_info(self):
        """显示缓存信息"""
        if not self.market_data_manager:
            return

        try:
            cache_info = self.market_data_manager.get_cache_info()

            info_text = f"""缓存目录: {cache_info['cache_dir']}
文件数量: {cache_info['total_files']}
总大小: {cache_info['total_size'] / 1024 / 1024:.2f} MB

文件列表:
"""

            for file_info in cache_info['files'][:10]:  # 只显示前10个文件
                info_text += f"- {file_info['name']} ({file_info['size'] / 1024:.1f} KB, {file_info['modified'].strftime('%Y-%m-%d %H:%M')})\n"

            if len(cache_info['files']) > 10:
                info_text += f"... 还有 {len(cache_info['files']) - 10} 个文件"

            messagebox.showinfo("缓存信息", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取缓存信息失败: {str(e)}")

    def clear_cache(self):
        """清理缓存"""
        if not self.market_data_manager:
            return

        if not messagebox.askyesno("确认", "确定要清理所有缓存文件吗？"):
            return

        try:
            result = self.market_data_manager.clear_cache()
            messagebox.showinfo("清理完成",
                              f"已删除 {result['deleted_files']} 个文件，"
                              f"释放 {result['deleted_size'] / 1024 / 1024:.2f} MB 空间")

        except Exception as e:
            messagebox.showerror("错误", f"清理缓存失败: {str(e)}")

    def show_cache_stats(self):
        """显示缓存统计信息"""
        if not self.market_data_manager:
            return

        try:
            stats = self.market_data_manager.get_cache_stats()

            disk_cache = stats['disk_cache']
            memory_cache = stats['memory_cache']

            info_text = f"""=== 缓存统计信息 ===

📁 磁盘缓存:
  缓存目录: {disk_cache['cache_dir']}
  文件数量: {disk_cache['total_files']}
  总大小: {disk_cache['total_size'] / 1024 / 1024:.2f} MB

💾 内存缓存:
  实时数据缓存: {memory_cache['realtime_items']} 项
  常用股票列表: {memory_cache['popular_stocks']} 只
  预加载状态: {'运行中' if memory_cache['preload_running'] else '已停止'}

🔧 缓存策略:
  • 交易时间内实时数据缓存5分钟
  • 非交易时间实时数据缓存1小时
  • 日线数据智能缓存1-6小时
  • 股票列表缓存1天
  • 行业数据缓存7天
"""

            messagebox.showinfo("缓存统计", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取缓存统计失败: {str(e)}")

    def clear_realtime_cache(self):
        """清理实时数据缓存"""
        if not self.market_data_manager:
            return

        if not messagebox.askyesno("确认", "确定要清理实时数据缓存吗？"):
            return

        try:
            self.market_data_manager.clear_realtime_cache()
            messagebox.showinfo("清理完成", "实时数据缓存已清理")

        except Exception as e:
            messagebox.showerror("错误", f"清理实时缓存失败: {str(e)}")

    def show_compression_analysis(self):
        """显示压缩效率分析"""
        if not self.market_data_manager:
            return

        try:
            analysis = self.market_data_manager.analyze_compression_efficiency()

            if 'error' in analysis:
                messagebox.showerror("错误", f"压缩分析失败: {analysis['error']}")
                return

            # 构建分析报告
            total_files = analysis['total_files']
            compressed_size_mb = analysis['total_compressed_size'] / 1024 / 1024
            uncompressed_size_mb = analysis['estimated_uncompressed_size'] / 1024 / 1024
            compression_ratio = analysis['compression_ratio']
            space_saved_mb = analysis['space_saved'] / 1024 / 1024

            info_text = f"""=== GZIP压缩效率分析 ===

📊 总体统计:
  缓存文件数量: {total_files}
  压缩后总大小: {compressed_size_mb:.2f} MB
  估算原始大小: {uncompressed_size_mb:.2f} MB
  压缩比: {compression_ratio:.1%}
  节省空间: {space_saved_mb:.2f} MB

🔍 文件详情:"""

            # 添加前5个文件的详细信息
            for i, file_info in enumerate(analysis['files_analysis'][:5]):
                file_name = file_info['file']
                file_compressed_mb = file_info['compressed_size'] / 1024 / 1024
                file_uncompressed_mb = file_info['uncompressed_size'] / 1024 / 1024
                file_ratio = file_info['compression_ratio']
                rows = file_info['rows']

                info_text += f"""

📁 {file_name}:
    压缩后: {file_compressed_mb:.2f} MB
    原始大小: {file_uncompressed_mb:.2f} MB
    压缩比: {file_ratio:.1%}
    数据行数: {rows:,}"""

            if len(analysis['files_analysis']) > 5:
                info_text += f"\n\n... 还有 {len(analysis['files_analysis']) - 5} 个文件"

            info_text += f"""

💡 压缩效果评估:
  {'🟢 优秀' if compression_ratio < 0.3 else '🟡 良好' if compression_ratio < 0.5 else '🟠 一般'}

🛠️ 技术细节:
  • 使用gzip压缩算法
  • 自适应压缩级别(1-9)
  • 原子性写入保证数据完整性
  • 损坏文件自动检测和清理"""

            messagebox.showinfo("压缩效率分析", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"压缩分析失败: {str(e)}")

    def show_api_usage_stats(self):
        """显示API使用统计"""
        if not self.market_data_manager:
            return

        try:
            usage_stats = self.market_data_manager.get_api_usage_stats()

            rate_limiter = usage_stats['rate_limiter']
            cache_stats = usage_stats['cache_stats']
            recommendations = usage_stats['recommendations']

            info_text = f"""=== API使用统计 ===

🚦 API限流状态:
  每分钟最大调用: {rate_limiter['max_calls_per_minute']}次
  当前已调用: {rate_limiter['calls_in_last_minute']}次
  剩余调用次数: {rate_limiter['remaining_calls']}次
  每次最大记录数: {rate_limiter['max_records_per_call']}条

📊 缓存使用情况:
  磁盘缓存文件: {cache_stats['disk_cache']['total_files']}个
  磁盘缓存大小: {cache_stats['disk_cache']['total_size'] / 1024 / 1024:.2f} MB
  内存缓存项: {cache_stats['memory_cache']['realtime_items']}个
  常用股票: {cache_stats['memory_cache']['popular_stocks']}只

💡 使用建议:"""

            for recommendation in recommendations:
                info_text += f"\n  {recommendation}"

            info_text += f"""

⚙️ 限流规则:
  • 每分钟最多450次API调用
  • 每次调用最多5000条记录
  • 自动等待机制避免超限
  • 优先使用缓存减少API调用

📈 优化效果:
  • 缓存命中可节省API调用
  • 批量获取提高效率
  • 智能等待避免限制
  • 实时监控使用状态"""

            messagebox.showinfo("API使用统计", info_text)

        except Exception as e:
            messagebox.showerror("错误", f"获取API统计失败: {str(e)}")

    def export_data(self, format_type):
        """导出数据"""
        if not self.market_data_manager:
            return

        try:
            from tkinter import filedialog

            # 选择保存文件
            if format_type == 'excel':
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )
            else:  # csv
                file_path = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
                )

            if file_path:
                self.market_data_manager.export_stock_list(file_path, format_type)
                messagebox.showinfo("导出成功", f"数据已导出到: {file_path}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def on_stock_double_click(self, event):
        """股票双击事件处理"""
        selection = self.stock_tree.selection()
        if not selection:
            return

        item = self.stock_tree.item(selection[0])
        ts_code = item['values'][0]

        # 切换到股票分析页面并查询该股票
        self.notebook.select(0)  # 选择第一个选项卡（股票分析）

        # 设置股票代码并查询
        self.code_entry.delete(0, tk.END)
        self.code_entry.insert(0, ts_code)

        # 自动查询股票数据
        self.query_stock()

    def __del__(self):
        """析构函数，清理资源"""
        try:
            if hasattr(self, 'web_driver') and self.web_driver:
                self.web_driver.quit()
        except:
            pass

    def run_stock_selection(self, parent_window):
        """运行策略指标选股"""
        def selection_thread():
            try:
                # 获取参数
                pool_type = self.selection_pool_var.get()
                strategy_type = self.selection_strategy_var.get()
                selection_date = self.selection_date_entry.get()
                lookback_days = int(self.lookback_days_entry.get())

                # 更新状态
                self.master.after(0, lambda: self.selection_status_label.config(text="获取股票池..."))
                self.master.after(0, lambda: self.selection_progress.config(value=10))

                # 获取股票池
                stock_pool = self.get_stock_pool(pool_type)
                if not stock_pool:
                    self.master.after(0, lambda: messagebox.showerror("错误", f"无法获取{pool_type}股票池"))
                    return

                self.master.after(0, lambda: self.selection_status_label.config(text=f"获取到{len(stock_pool)}只股票"))
                self.master.after(0, lambda: self.selection_progress.config(value=20))

                # 计算结束日期
                from datetime import datetime, timedelta
                end_date = datetime.strptime(selection_date, "%Y%m%d")
                start_date = end_date - timedelta(days=lookback_days)
                start_date_str = start_date.strftime("%Y%m%d")

                # 批量获取股票数据并计算指标
                selection_results = []
                total_stocks = len(stock_pool)

                for i, stock_code in enumerate(stock_pool):
                    try:
                        # 更新进度
                        progress = 20 + (i / total_stocks) * 70
                        self.master.after(0, lambda p=progress: self.selection_progress.config(value=p))
                        self.master.after(0, lambda s=stock_code: self.selection_status_label.config(text=f"分析 {s}..."))

                        # 获取股票数据
                        if self.market_data_manager:
                            data = self.market_data_manager.get_stock_daily_data(
                                ts_code=stock_code,
                                start_date=start_date_str,
                                end_date=selection_date,
                                use_cache=True
                            )
                        else:
                            data = self.pro.daily(ts_code=stock_code, start_date=start_date_str, end_date=selection_date)
                            data['trade_date'] = pd.to_datetime(data['trade_date'], format='%Y%m%d')
                            data = data.sort_values('trade_date')

                        if data.empty:
                            continue

                        # 使用策略买点信号进行选股
                        buy_signal_result = self.check_strategy_buy_signal(data, strategy_type, stock_code)

                        if buy_signal_result['has_signal']:
                            # 获取股票名称
                            stock_name = self.get_stock_name(stock_code)

                            selection_results.append({
                                'stock_code': stock_code,
                                'stock_name': stock_name,
                                'indicator_value': buy_signal_result['indicator_value'],
                                'signal_strength': buy_signal_result['signal_strength'],
                                'latest_price': data['close'].iloc[-1] if not data.empty else 0,
                                'price_change': buy_signal_result.get('price_change', 0),
                                'signal_date': buy_signal_result.get('signal_date', ''),
                                'signal_info': buy_signal_result.get('signal_info', '')
                            })

                    except Exception as e:
                        print(f"分析股票 {stock_code} 失败: {str(e)}")
                        continue

                # 按信号强度排序
                selection_results.sort(key=lambda x: x['signal_strength'], reverse=True)

                # 保存结果
                self.stock_selection_results = {
                    'results': selection_results,
                    'parameters': {
                        'pool_type': pool_type,
                        'strategy_type': strategy_type,
                        'selection_date': selection_date,
                        'lookback_days': lookback_days
                    }
                }

                # 显示结果
                self.master.after(0, lambda: self.display_selection_results(selection_results))
                self.master.after(0, lambda: self.selection_progress.config(value=100))
                self.master.after(0, lambda: self.selection_status_label.config(text=f"选股完成，找到{len(selection_results)}只股票"))

            except Exception as e:
                error_msg = f"选股失败: {str(e)}"
                self.master.after(0, lambda: messagebox.showerror("错误", error_msg))
                self.master.after(0, lambda: self.selection_status_label.config(text="选股失败"))
                self.master.after(0, lambda: self.selection_progress.config(value=0))

        # 清空结果显示
        for item in self.selection_tree.get_children():
            self.selection_tree.delete(item)
        self.selection_progress.config(value=0)

        # 在新线程中运行选股
        thread = threading.Thread(target=selection_thread)
        thread.daemon = True
        thread.start()

    def set_realtime_date(self):
        """设置为实时日期"""
        from datetime import datetime
        current_date = datetime.now().strftime("%Y%m%d")
        self.selection_date_entry.delete(0, tk.END)
        self.selection_date_entry.insert(0, current_date)

    def get_stock_pool(self, pool_type):
        """获取股票池"""
        try:
            if pool_type == "自定义列表":
                # 使用多股票回测中的股票列表
                return self.multi_stock_list if hasattr(self, 'multi_stock_list') else []
            elif self.market_data_manager:
                # 使用市场数据管理器获取板块股票
                if pool_type == "全部A股":
                    # 获取所有A股股票
                    return self.market_data_manager.get_all_stocks()
                elif pool_type == "主板":
                    return self.market_data_manager.get_sector_stocks_enhanced("主板")
                elif pool_type == "北交所":
                    return self.market_data_manager.get_sector_stocks_enhanced("北交所")
                else:
                    return self.market_data_manager.get_sector_stocks_enhanced(pool_type)
            else:
                # 简化版股票池
                if pool_type == "沪深300":
                    return ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "600519.SH",
                           "000858.SZ", "002415.SZ", "600276.SH", "600030.SH", "000166.SZ"]  # 示例股票
                elif pool_type == "上证50":
                    return ["600000.SH", "600036.SH", "600519.SH", "600887.SH", "601318.SH"]  # 示例股票
                elif pool_type == "全部A股":
                    # 如果没有市场数据管理器，返回更多示例股票
                    return self.get_sample_all_stocks()
                else:
                    return ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]  # 默认股票池
        except Exception as e:
            print(f"获取股票池失败: {str(e)}")
            return []

    def get_sample_all_stocks(self):
        """获取示例全市场股票（当市场数据管理器不可用时）"""
        # 返回一个较大的示例股票池，涵盖各个板块
        sample_stocks = [
            # 主板大盘股
            "000001.SZ", "000002.SZ", "000858.SZ", "000166.SZ", "000725.SZ",
            "600000.SH", "600036.SH", "600519.SH", "600276.SH", "600030.SH",
            "600887.SH", "601318.SH", "601398.SH", "601939.SH", "601288.SH",
            # 创业板
            "300015.SZ", "300059.SZ", "300142.SZ", "300274.SZ", "300408.SZ",
            "300750.SZ", "300760.SZ", "300896.SZ", "300999.SZ",
            # 科创板
            "688001.SH", "688009.SH", "688036.SH", "688111.SH", "688169.SH",
            "688188.SH", "688223.SH", "688303.SH", "688599.SH",
            # 中小板
            "002415.SZ", "002594.SZ", "002714.SZ", "002841.SZ", "002916.SZ"
        ]
        return sample_stocks

    def get_stock_name(self, stock_code):
        """获取股票名称"""
        try:
            if self.market_data_manager:
                # 尝试从市场数据管理器获取股票名称
                stock_info = self.market_data_manager.get_stock_basic_info(stock_code)
                if stock_info and not stock_info.empty:
                    return stock_info['name'].iloc[0]

            # 简化版股票名称映射
            name_map = {
                "000001.SZ": "平安银行",
                "000002.SZ": "万科A",
                "600000.SH": "浦发银行",
                "600036.SH": "招商银行",
                "600519.SH": "贵州茅台",
                "600887.SH": "伊利股份",
                "601318.SH": "中国平安"
            }
            return name_map.get(stock_code, stock_code.split('.')[0])
        except:
            return stock_code.split('.')[0]

    def check_strategy_buy_signal(self, data, strategy_type, stock_code):
        """检查策略买点信号"""
        try:
            # 创建策略实例
            if strategy_type == "MACD":
                from 回测系统 import MACDStrategy
                strategy = MACDStrategy()
                # 设置参数
                if hasattr(self, 'macd_fast_var'):
                    strategy.set_parameters(
                        fast_period=int(self.macd_fast_var.get()),
                        slow_period=int(self.macd_slow_var.get()),
                        signal_period=int(self.macd_signal_var.get())
                    )
                else:
                    strategy.set_parameters(fast_period=12, slow_period=26, signal_period=9)

            elif strategy_type == "KDJ":
                from 回测系统 import KDJStrategy
                strategy = KDJStrategy()
                # 设置参数
                if hasattr(self, 'kdj_period_var'):
                    strategy.set_parameters(
                        k_period=int(self.kdj_period_var.get()),
                        oversold=int(self.kdj_oversold_var.get()),
                        overbought=int(self.kdj_overbought_var.get())
                    )
                else:
                    strategy.set_parameters(k_period=9, oversold=20, overbought=80)

            elif strategy_type == "自定义策略":
                if not hasattr(self, 'custom_strategy_code') or not self.custom_strategy_code.strip():
                    return {'has_signal': False, 'indicator_value': 0, 'signal_strength': 0, 'signal_info': '未设置自定义策略'}

                from 回测系统 import CustomStrategy
                strategy = CustomStrategy("自定义策略", self.custom_strategy_code)
            else:
                return {'has_signal': False, 'indicator_value': 0, 'signal_strength': 0, 'signal_info': '未知策略类型'}

            # 运行策略生成信号
            data_with_signals = strategy.generate_signals(data.copy())

            # 检查最近几天是否有买入信号
            recent_days = min(5, len(data_with_signals))  # 检查最近5天
            buy_signals = []

            for i in range(len(data_with_signals) - recent_days, len(data_with_signals)):
                if i >= 0 and data_with_signals.iloc[i]['signal'] == 1:  # 买入信号
                    signal_date = data_with_signals.index[i] if hasattr(data_with_signals.index[i], 'strftime') else str(data_with_signals.index[i])
                    signal_price = data_with_signals.iloc[i]['close']
                    buy_signals.append({
                        'date': signal_date,
                        'price': signal_price,
                        'index': i
                    })

            if buy_signals:
                # 使用最新的买入信号
                latest_signal = buy_signals[-1]
                latest_price = data_with_signals['close'].iloc[-1]
                signal_price = latest_signal['price']

                # 计算信号强度（基于价格变化和信号新鲜度）
                price_change = (latest_price - signal_price) / signal_price * 100
                days_since_signal = len(data_with_signals) - 1 - latest_signal['index']
                signal_strength = max(1, 10 - days_since_signal * 2)  # 信号越新强度越高

                # 获取指标值（根据策略类型）
                indicator_value = 0
                if strategy_type == "MACD" and 'dif' in data_with_signals.columns:
                    indicator_value = data_with_signals['dif'].iloc[-1]
                elif strategy_type == "KDJ" and 'k' in data_with_signals.columns:
                    indicator_value = data_with_signals['k'].iloc[-1]
                elif strategy_type == "自定义策略":
                    indicator_value = data_with_signals['signal'].iloc[-1]

                return {
                    'has_signal': True,
                    'indicator_value': indicator_value,
                    'signal_strength': signal_strength,
                    'price_change': price_change,
                    'signal_date': latest_signal['date'],
                    'signal_info': f"{strategy_type}策略买点信号"
                }
            else:
                return {
                    'has_signal': False,
                    'indicator_value': 0,
                    'signal_strength': 0,
                    'signal_info': f"近期无{strategy_type}买点信号"
                }

        except Exception as e:
            print(f"检查策略买点信号失败 {stock_code}: {str(e)}")
            return {
                'has_signal': False,
                'indicator_value': 0,
                'signal_strength': 0,
                'signal_info': f"信号检查失败: {str(e)}"
            }

    def calculate_indicator_signal(self, data, indicator_type, condition_type):
        """计算指标信号"""
        try:
            if indicator_type == "MACD":
                return self.calculate_macd_signal(data, condition_type)
            elif indicator_type == "KDJ":
                return self.calculate_kdj_signal(data, condition_type)
            elif indicator_type == "RSI":
                return self.calculate_rsi_signal(data, condition_type)
            elif indicator_type == "BOLL":
                return self.calculate_boll_signal(data, condition_type)
            elif indicator_type == "MA":
                return self.calculate_ma_signal(data, condition_type)
            else:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}
        except Exception as e:
            print(f"计算指标信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def calculate_macd_signal(self, data, condition_type):
        """计算MACD信号"""
        try:
            fast_period = int(self.macd_fast_var.get())
            slow_period = int(self.macd_slow_var.get())
            signal_period = int(self.macd_signal_var.get())

            # 计算MACD
            ewam_fast = data['close'].ewm(span=fast_period, adjust=False).mean()
            ewam_slow = data['close'].ewm(span=slow_period, adjust=False).mean()
            dif = ewam_fast - ewam_slow
            dea = dif.ewm(span=signal_period, adjust=False).mean()
            macd = 2 * (dif - dea)

            if len(dif) < 2:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

            # 判断信号
            current_dif = dif.iloc[-1]
            current_dea = dea.iloc[-1]
            prev_dif = dif.iloc[-2]
            prev_dea = dea.iloc[-2]

            signal = False
            signal_strength = 0

            if condition_type == "金叉":
                # DIF上穿DEA
                if prev_dif <= prev_dea and current_dif > current_dea:
                    signal = True
                    signal_strength = abs(current_dif - current_dea) * 100
            elif condition_type == "死叉":
                # DIF下穿DEA
                if prev_dif >= prev_dea and current_dif < current_dea:
                    signal = True
                    signal_strength = abs(current_dif - current_dea) * 100

            return {
                'signal': signal,
                'indicator_value': current_dif,
                'signal_strength': signal_strength,
                'price_change': (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100 if len(data) >= 2 else 0
            }
        except Exception as e:
            print(f"计算MACD信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def calculate_kdj_signal(self, data, condition_type):
        """计算KDJ信号"""
        try:
            period = int(self.kdj_period_var.get())

            # 计算KDJ
            low_list = data['low'].rolling(period, min_periods=period).min()
            high_list = data['high'].rolling(period, min_periods=period).max()
            rsv = (data['close'] - low_list) / (high_list - low_list) * 100
            k = rsv.ewm(com=2).mean()
            d = k.ewm(com=2).mean()
            j = 3 * k - 2 * d

            if len(k) < 2:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

            current_k = k.iloc[-1]
            current_d = d.iloc[-1]
            prev_k = k.iloc[-2]
            prev_d = d.iloc[-2]

            signal = False
            signal_strength = 0

            if condition_type == "金叉":
                if prev_k <= prev_d and current_k > current_d and current_k < 80:
                    signal = True
                    signal_strength = abs(current_k - current_d)
            elif condition_type == "死叉":
                if prev_k >= prev_d and current_k < current_d and current_k > 20:
                    signal = True
                    signal_strength = abs(current_k - current_d)
            elif condition_type == "超买":
                if current_k > 80 and current_d > 80:
                    signal = True
                    signal_strength = min(current_k, current_d) - 80
            elif condition_type == "超卖":
                if current_k < 20 and current_d < 20:
                    signal = True
                    signal_strength = 20 - max(current_k, current_d)

            return {
                'signal': signal,
                'indicator_value': current_k,
                'signal_strength': signal_strength,
                'price_change': (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100 if len(data) >= 2 else 0
            }
        except Exception as e:
            print(f"计算KDJ信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def calculate_rsi_signal(self, data, condition_type):
        """计算RSI信号"""
        try:
            period = int(self.rsi_period_var.get())
            overbought = float(self.rsi_overbought_var.get())
            oversold = float(self.rsi_oversold_var.get())

            # 计算RSI
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            if len(rsi) < 1:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

            current_rsi = rsi.iloc[-1]
            signal = False
            signal_strength = 0

            if condition_type == "超买":
                if current_rsi > overbought:
                    signal = True
                    signal_strength = current_rsi - overbought
            elif condition_type == "超卖":
                if current_rsi < oversold:
                    signal = True
                    signal_strength = oversold - current_rsi

            return {
                'signal': signal,
                'indicator_value': current_rsi,
                'signal_strength': signal_strength,
                'price_change': (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100 if len(data) >= 2 else 0
            }
        except Exception as e:
            print(f"计算RSI信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def calculate_boll_signal(self, data, condition_type):
        """计算布林带信号"""
        try:
            period = int(self.boll_period_var.get())
            std_dev = float(self.boll_std_var.get())

            # 计算布林带
            ma = data['close'].rolling(window=period).mean()
            std = data['close'].rolling(window=period).std()
            upper = ma + (std * std_dev)
            lower = ma - (std * std_dev)

            if len(ma) < 1:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

            current_price = data['close'].iloc[-1]
            current_upper = upper.iloc[-1]
            current_lower = lower.iloc[-1]
            current_ma = ma.iloc[-1]

            signal = False
            signal_strength = 0

            if condition_type == "突破":
                if current_price > current_upper:
                    signal = True
                    signal_strength = (current_price - current_upper) / current_upper * 100
            elif condition_type == "跌破":
                if current_price < current_lower:
                    signal = True
                    signal_strength = (current_lower - current_price) / current_lower * 100

            return {
                'signal': signal,
                'indicator_value': (current_price - current_ma) / current_ma * 100,
                'signal_strength': signal_strength,
                'price_change': (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100 if len(data) >= 2 else 0
            }
        except Exception as e:
            print(f"计算布林带信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def calculate_ma_signal(self, data, condition_type):
        """计算均线信号"""
        try:
            short_period = int(self.ma_short_var.get())
            long_period = int(self.ma_long_var.get())

            # 计算均线
            ma_short = data['close'].rolling(window=short_period).mean()
            ma_long = data['close'].rolling(window=long_period).mean()

            if len(ma_short) < 2 or len(ma_long) < 2:
                return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

            current_short = ma_short.iloc[-1]
            current_long = ma_long.iloc[-1]
            prev_short = ma_short.iloc[-2]
            prev_long = ma_long.iloc[-2]

            signal = False
            signal_strength = 0

            if condition_type == "金叉":
                if prev_short <= prev_long and current_short > current_long:
                    signal = True
                    signal_strength = (current_short - current_long) / current_long * 100
            elif condition_type == "死叉":
                if prev_short >= prev_long and current_short < current_long:
                    signal = True
                    signal_strength = (current_long - current_short) / current_long * 100

            return {
                'signal': signal,
                'indicator_value': (current_short - current_long) / current_long * 100,
                'signal_strength': abs(signal_strength),
                'price_change': (data['close'].iloc[-1] / data['close'].iloc[-2] - 1) * 100 if len(data) >= 2 else 0
            }
        except Exception as e:
            print(f"计算均线信号失败: {str(e)}")
            return {'signal': False, 'indicator_value': 0, 'signal_strength': 0}

    def display_selection_results(self, results):
        """显示选股结果"""
        # 清空现有结果
        for item in self.selection_tree.get_children():
            self.selection_tree.delete(item)

        # 插入新结果
        for i, result in enumerate(results, 1):
            # 根据信号强度设置颜色
            if result['signal_strength'] > 5:
                tag = 'strong'
            elif result['signal_strength'] > 2:
                tag = 'medium'
            else:
                tag = 'weak'

            # 格式化信号日期
            signal_date = result.get('signal_date', '')
            if signal_date and len(str(signal_date)) > 10:
                signal_date = str(signal_date)[:10]  # 只显示日期部分

            self.selection_tree.insert('', 'end', values=(
                i,
                result['stock_code'],
                result['stock_name'],
                result.get('signal_info', f"{result['indicator_value']:.3f}"),
                f"{result['signal_strength']:.2f}",
                f"{result['latest_price']:.2f}",
                f"{result['price_change']:.2f}%",
                signal_date
            ), tags=(tag,))

        # 配置标签颜色
        self.selection_tree.tag_configure('strong', foreground='red', font=('Arial', 9, 'bold'))
        self.selection_tree.tag_configure('medium', foreground='orange')
        self.selection_tree.tag_configure('weak', foreground='gray')

    def show_selection_results(self):
        """显示选股结果窗口"""
        if not hasattr(self, 'stock_selection_results') or not self.stock_selection_results:
            messagebox.showwarning("提示", "请先运行指标选股")
            return

        results_window = tk.Toplevel(self.master)
        results_window.title("选股结果详情")
        results_window.geometry("800x600")

        # 参数信息
        params = self.stock_selection_results['parameters']
        info_frame = tk.LabelFrame(results_window, text="选股参数", padx=10, pady=10)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        info_text = f"""股票池: {params['pool_type']}
策略类型: {params['strategy_type']}
选股日期: {params['selection_date']}
回看天数: {params['lookback_days']}
选中股票: {len(self.stock_selection_results['results'])}只"""

        tk.Label(info_frame, text=info_text, justify=tk.LEFT, font=('Arial', 10)).pack(anchor='w')

        # 结果列表
        result_frame = tk.LabelFrame(results_window, text="详细结果", padx=10, pady=10)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        result_text = scrolledtext.ScrolledText(result_frame, height=20, width=80, font=('Consolas', 9))
        result_text.pack(fill=tk.BOTH, expand=True)

        # 生成详细报告
        report = self.generate_selection_report()
        result_text.insert(tk.END, report)
        result_text.config(state='disabled')

    def generate_selection_report(self):
        """生成选股报告"""
        if not hasattr(self, 'stock_selection_results') or not self.stock_selection_results:
            return "暂无选股结果"

        results = self.stock_selection_results['results']
        params = self.stock_selection_results['parameters']

        report = []
        report.append("=" * 80)
        report.append("策略指标选股报告")
        report.append("=" * 80)

        # 选股参数
        report.append(f"\n【选股参数】")
        report.append(f"股票池: {params['pool_type']}")
        report.append(f"策略类型: {params['strategy_type']}")
        report.append(f"选股日期: {params['selection_date']}")
        report.append(f"数据回看: {params['lookback_days']}天")

        # 选股结果统计
        report.append(f"\n【选股结果统计】")
        report.append(f"符合条件股票: {len(results)}只")

        if results:
            avg_strength = sum(r['signal_strength'] for r in results) / len(results)
            max_strength = max(r['signal_strength'] for r in results)
            min_strength = min(r['signal_strength'] for r in results)

            report.append(f"平均信号强度: {avg_strength:.2f}")
            report.append(f"最强信号强度: {max_strength:.2f}")
            report.append(f"最弱信号强度: {min_strength:.2f}")

            # 按信号强度分类
            strong_signals = [r for r in results if r['signal_strength'] > 5]
            medium_signals = [r for r in results if 2 < r['signal_strength'] <= 5]
            weak_signals = [r for r in results if r['signal_strength'] <= 2]

            report.append(f"强信号股票: {len(strong_signals)}只")
            report.append(f"中等信号股票: {len(medium_signals)}只")
            report.append(f"弱信号股票: {len(weak_signals)}只")

        # 详细结果
        report.append(f"\n【详细选股结果】")
        report.append("-" * 100)
        report.append(f"{'排名':<4} {'股票代码':<12} {'股票名称':<12} {'策略信号':<20} {'信号强度':<10} {'最新价格':<10} {'涨跌幅':<10} {'信号日期':<12}")
        report.append("-" * 100)

        for i, result in enumerate(results, 1):
            signal_info = result.get('signal_info', f"{result['indicator_value']:.3f}")
            signal_date = str(result.get('signal_date', ''))[:10] if result.get('signal_date') else ''

            report.append(f"{i:<4} {result['stock_code']:<12} {result['stock_name']:<12} "
                         f"{signal_info:<20} {result['signal_strength']:<10.2f} "
                         f"{result['latest_price']:<10.2f} {result['price_change']:<10.2f}% {signal_date:<12}")

        return "\n".join(report)

    def export_selection_results(self):
        """导出选股结果"""
        if not hasattr(self, 'stock_selection_results') or not self.stock_selection_results:
            messagebox.showwarning("提示", "请先运行指标选股")
            return

        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("CSV files", "*.csv"), ("Text files", "*.txt")],
                title="导出选股结果"
            )

            if filename:
                results = self.stock_selection_results['results']

                if filename.endswith('.xlsx'):
                    # 导出为Excel
                    df = pd.DataFrame(results)
                    df.to_excel(filename, index=False)
                elif filename.endswith('.csv'):
                    # 导出为CSV
                    df = pd.DataFrame(results)
                    df.to_csv(filename, index=False, encoding='utf-8-sig')
                else:
                    # 导出为文本
                    report = self.generate_selection_report()
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(report)

                messagebox.showinfo("导出成功", f"选股结果已导出到:\n{filename}")
        except Exception as e:
            messagebox.showerror("导出失败", f"导出选股结果失败: {str(e)}")

    def apply_to_multi_backtest(self):
        """将选股结果应用到多股票回测"""
        if not hasattr(self, 'stock_selection_results') or not self.stock_selection_results:
            messagebox.showwarning("提示", "请先运行指标选股")
            return

        results = self.stock_selection_results['results']
        if not results:
            messagebox.showwarning("提示", "选股结果为空")
            return

        # 提取股票代码
        stock_codes = [result['stock_code'] for result in results]

        # 切换到多股票回测选项卡
        self.notebook.select(3)  # 多股票回测选项卡

        # 清空现有股票列表
        self.multi_stock_list.clear()
        self.stock_listbox.delete(0, tk.END)

        # 添加选股结果到多股票回测列表
        for stock_code in stock_codes:
            self.multi_stock_list.append(stock_code)
            self.stock_listbox.insert(tk.END, stock_code)

        self.multi_status_label.config(text=f"已导入{len(stock_codes)}只选股结果")

        messagebox.showinfo("导入成功",
                          f"已将{len(stock_codes)}只选股结果导入到多股票回测列表\n"
                          f"您可以在多股票回测页面进行回测验证")


if __name__ == "__main__":
    root = tk.Tk()
    app = EnhancedStockViewerApp(root)
    root.mainloop()
