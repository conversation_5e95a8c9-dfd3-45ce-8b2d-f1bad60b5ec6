# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 需要包含的数据文件和模块
added_files = [
    ('股票看图软件_增强版.py', '.'),
    ('回测系统.py', '.'),
    ('回测分析.py', '.'),
    ('策略模板.py', '.'),
    ('多股票回测系统.py', '.'),
    ('技术指标库.py', '.'),
    ('市场数据管理.py', '.'),
    ('使用者监控.py', '.'),
    ('token_manager.py', '.'),
    ('浏览器驱动管理.py', '.'),
    ('策略示例', '策略示例'),
    ('user_config', 'user_config'),
    ('market_data_cache', 'market_data_cache'),
]

a = Analysis(
    ['登录注册.py'],  # 以登录注册.py作为启动入口
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=[
        # 基础库
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'tkinter.filedialog',
        
        # 数据处理库
        'pandas',
        'numpy',
        'matplotlib',
        'matplotlib.pyplot',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.backends.backend_tkagg',
        
        # 网络和API库
        'requests',
        'tushare',
        'lxml',
        'bs4',
        'urllib3',
        
        # 浏览器自动化库
        'selenium',
        'selenium.webdriver',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support',
        'selenium.webdriver.support.expected_conditions',
        'selenium.webdriver.edge.service',
        'selenium.webdriver.edge.options',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'webdriver_manager',
        'webdriver_manager.microsoft',
        'webdriver_manager.chrome',
        
        # 图像处理库
        'PIL',
        'PIL.Image',
        'PIL.ImageTk',
        
        # Excel处理库
        'openpyxl',
        'xlrd',
        'xlsxwriter',
        
        # 系统和工具库
        'datetime',
        'threading',
        'json',
        'time',
        'os',
        'sys',
        'subprocess',
        'importlib',
        'importlib.util',
        'base64',
        'io',
        'hashlib',
        'gzip',
        'pickle',
        'collections',
        'concurrent.futures',
        'psutil',
        'platform',
        
        # 自定义模块
        '回测系统',
        '回测分析',
        '策略模板',
        '多股票回测系统',
        '技术指标库',
        '市场数据管理',
        '使用者监控',
        'token_manager',
        '浏览器驱动管理',
        '股票看图软件_增强版',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的大型库
        'PyQt5',
        'PySide6',
        'PyQt6',
        'PySide2',
        'torch',
        'tensorflow',
        'cv2',
        'sklearn',
        'scipy.sparse.csgraph._validation',
        'IPython',
        'jupyter',
        'notebook',
        'spyder',
        'pytest',
        'unittest',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='量化股票软件',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
)
